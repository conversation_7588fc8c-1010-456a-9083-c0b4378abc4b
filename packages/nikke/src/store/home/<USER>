import { computed, ref } from "vue";
import { defineStore } from "pinia";
import { STORE_KEY } from "@/configs/store.ts";
import { useGrayscale } from "packages/utils/grayscale";
import { useCDNConfigs } from "packages/utils/cdn";
import { Gray<PERSON>leKey } from "packages/types/grayscale";

enum AnniversaryValue {
  two_and_half = "2.5",
  atmosphere = "atmosphere",
}

export const useAnniversary = defineStore(STORE_KEY.HOME.ANN, () => {
  /** @deprecated using anniversary_visible instead */
  const anniversary = ref(false);

  const anniversary_visible = ref(false);
  const anniversary_value = ref();
  const anniversary_bg_config = ref({
    bg_image: "",
    bg_color: "",
  });

  const { getCDNConfigs } = useCDNConfigs();

  const handleAnniversary = async () => {
    const { getGrayscale } = useGrayscale();
    const visible = await getGrayscale(GrayscaleKey.annual_event, {});
    console.log("[handleAnniversary] visible", visible);
    anniversary.value = visible;
    anniversary_visible.value = visible;
    // anniversary.value = false;

    const { annual_event } = await getCDNConfigs();
    if (annual_event) {
      const { value, configs } = annual_event;
      const target_confg = configs.find((config) => config.value === value);

      anniversary_value.value = value;
      target_confg && (anniversary_bg_config.value = target_confg?.bg_config);
    }

    console.log(`[handleAnniversary] anniversary_value`, anniversary_value.value);
    console.log(`[handleAnniversary] anniversary_bg_config`, anniversary_bg_config.value);
  };

  const isAnniversaryType = (type: AnniversaryValue) => {
    return anniversary_value.value === type;
  };

  const is_two_and_half_anniversary = computed(() => {
    return anniversary_visible.value && isAnniversaryType(AnniversaryValue.two_and_half);
  });

  const is_atmosphere_anniversary = computed(() => {
    return anniversary_visible.value && isAnniversaryType(AnniversaryValue.atmosphere);
  });

  handleAnniversary();

  return {
    anniversary,
    anniversary_value,
    anniversary_visible,
    anniversary_bg_config,

    is_two_and_half_anniversary,
    is_atmosphere_anniversary,
  };
});
