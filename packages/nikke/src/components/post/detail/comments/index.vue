<template>
  <div
    ref="comment_area_ref"
    class="flex items-center justify-between px-[16px] pt-[10px] border-t-[6px] border-color-[var(--line-1)]"
  >
    <div
      class="text-[color:var(--text-1)] text-[length:var(--font-size-langer)] leading-[1] flex-1 flex items-center"
    >
      <SvgIcon
        name="icon-post"
        class="w-[24px] h-[24px] mr-[2px] p-[3px]"
        color="var(--text-1)"
      ></SvgIcon>
      <span class="font-Abolition">{{ t("comments") }}</span>
    </div>
    <DropdownNormal
      :list="sort_options"
      :active="current_sort"
      side="bottom"
      align="end"
      @change="(v: any) => (current_sort = v.value)"
    >
      <template #trigger="{ item }">
        <SelectHead :text="item.name" :anniversary_visible="false" />
      </template>
    </DropdownNormal>
  </div>

  <div>
    <InfiniteScroll
      :back_to_top_visible="false"
      :loading="loading"
      :finished="finished"
      :empty="comment_list.length === 0"
      @load-more="load"
    >
      <CommentItem
        v-for="(item, comment_item_index) in comment_list"
        :key="item.comment_uuid"
        :item="item"
        :bg_flash="comment_item_index === 0 && bg_flash"
        :index="comment_item_index"
        :class="[
          'py-[15px] px-[15px]',
          comment_item_index !== comment_list.length - 1 &&
            'border-b-[1px] border-color-[var(--line-1)]',
        ]"
        @translate="comments_store.onTranslateComment(item)"
        @upvote="comments_store.onCommentStar(item, CommentType.comment)"
        @more="
          showPop({
            type: 'more',
            ignores: comments_store.onGetMoreIgnoreItemList(item),
            overrides: {
              [ActionType.top_comment]: {
                text: [TopBottomStatus.unset, TopBottomStatus.bottom].includes(
                  item.top_bottom_status,
                )
                  ? t('top_comment')
                  : t('cancel_topping'),
              },
              [ActionType.bottom_comment]: {
                text: [TopBottomStatus.unset, TopBottomStatus.top].includes(item.top_bottom_status)
                  ? t('bottom_comment')
                  : t('cancel_bottoming'),
              },
            },
            onClick: (type: ActionType) =>
              comments_store.onMoreItemClick(item, type, { item_index: comment_item_index }),
          })
        "
        @reply="
          showCommentsPop({
            post_uuid: item.post_uuid,
            comment_uuid: item.comment_uuid,
            type: CommentType.reply,
          })
        "
      >
        <template v-if="getReplyList(item).length > 0" #replies="{ item }">
          <ReplyItem
            v-for="(reply_item, reply_item_index) in getReplyList(item)"
            :key="reply_item.reply_uuid"
            :item="reply_item"
            :index="reply_item_index"
            @translate="comments_store.onTranslateComment(reply_item)"
            @upvote="comments_store.onCommentStar(reply_item, CommentType.reply)"
            @more="
              showPop({
                type: 'more',
                ignores: comments_store
                  .onGetMoreIgnoreItemList(reply_item)
                  .concat([ActionType.copy_comment_id]),
                onClick: (type: ActionType) =>
                  comments_store.onMoreItemClick(reply_item, type, {
                    report_content_type: ReportContentType.comment,
                    item_index: reply_item_index,
                  }),
              })
            "
            @reply="
              showCommentsPop({
                post_uuid: reply_item.post_uuid,
                comment_uuid: reply_item.comment_uuid,
                type: CommentType.reply,
                parent_uuid: item.comment_uuid,
              })
            "
          ></ReplyItem>

          <div
            v-if="get(item, 'replies.page_info.total', 0) > 3"
            class="flex cursor-pointer items-center text-[color:var(--brand-1)] text-[length:12px] leading-[16px] font-bold"
            @click="
              router.push({
                path: Routes.POST_COMMENTS,
                query: { comment_uuid: item.comment_uuid, post_uuid: item.post_uuid },
              })
            "
          >
            {{ get(item, "replies.page_info.total", 0) }} {{ t("comments") }}
            <SvgIcon
              class="w-[12px] h-[12px] rotate-[180deg] ml-[6px]"
              name="icon-goback"
              color="var(--brand-1)"
            ></SvgIcon>
          </div>
        </template>
      </CommentItem>

      <template #empty>
        <Nodata
          class="mt-[70px]"
          first
          :text="
            t('come_to_grab', [`<span class='text-[var(--brand-1)]'>${t('first_comment')}</span>`])
          "
          @click="onNoDataClick"
        ></Nodata>
      </template>

      <template #finished>
        <div class="flex justify-center w-full items-center text-[var(--text-4)] text-[13px]">
          {{ t("loaded_comments") }}
        </div>
      </template>
    </InfiniteScroll>
  </div>
</template>

<script setup lang="ts">
// cpnts
import CommentItem from "@/components/post/comments/item/comment.vue";
import ReplyItem from "@/components/post/comments/item/reply.vue";
import Nodata from "@/components/common/nodata.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import InfiniteScroll from "@/components/common/scroll/infinite-scroll.vue";
import DropdownNormal from "@/components/common/dropdown/index.vue";
import { usePop } from "@/components/post/detail/pop/index.ts";

// types
import { CommentType, GetPostCommentsResponseItem, TopBottomStatus } from "packages/types/comments";
import { ActionType } from "packages/types/post";
import { ReportContentType } from "packages/types/content";

// configs
import { STORAGE_LS_POST_DETAIL_COMMENT_SORT } from "packages/configs/storage";

// utils
import { computed, onActivated, onMounted, onUnmounted, ref, watch } from "vue";
import { t } from "@/locales";
import { useComments } from "@/store/post/comments";
import { get } from "lodash-es";
import { useRoute, useRouter } from "vue-router";
import { Routes } from "@/router/routes";
import { useCommentsPop } from "@/components/post/comments/pop/index.ts";
import { urlSearchObjectify } from "packages/utils/qs";
import { useIsDeleted } from "@/composables/use-is-deleted.ts";
import { useUser } from "@/store/user";
import { useToast } from "@/components/ui/toast";
import { useStorage } from "@vueuse/core";
import SelectHead from "@/components/common/select-head/index.vue";
import { event_emitter, EVENT_NAMES } from "packages/utils/event-emitter";

const { show: showCommentsPop } = useCommentsPop();
const user_store = useUser();
const { show: toast } = useToast();
const { show: showPop } = usePop();

const sort_options = [
  { name: t("latest"), type: "time", value: 1 },
  { name: t("hot"), type: "hot", value: 2 },
];
const current_sort = useStorage(STORAGE_LS_POST_DETAIL_COMMENT_SORT, 2);

const route = useRoute();
const router = useRouter();
const comments_store = useComments();
const { filterIsDeleted } = useIsDeleted();
const url_object = urlSearchObjectify();

const comment_area_ref = ref();
const bg_flash = ref(false);

comments_store.onResetState();
const { load, loading, finished, reset } = comments_store.onGetPostCommments({
  order_by: current_sort,
});

watch(current_sort, () => {
  reset();
});

onActivated(() => {
  if (
    route.meta.needRefresh ||
    route.query.post_uuid !== comments_store.state?.get_post_comments_params?.post_uuid
  ) {
    reset();
  }
});

const comment_list = computed(() => filterIsDeleted(comments_store.state.comment_list));

const getReplyList = (item: GetPostCommentsResponseItem) => {
  return filterIsDeleted(get(item, "replies.data_list", [])).slice(0, 3);
};

const onNoDataClick = () => {
  if (user_store.is_login && user_store.user_info?.is_mute) {
    toast({ text: t("violation_tips"), type: "error" });
    return;
  }
  showCommentsPop({
    post_uuid: url_object.post_uuid,
    type: CommentType.comment,
  });
};

const onScrollIntoCommentAreaView = () => {
  console.log(`[onScrollIntoCommentAreaView] called`, comment_area_ref.value);
  comment_area_ref.value?.scrollIntoView({
    behavior: "smooth",
  });
};

onMounted(() => {
  event_emitter.on(EVENT_NAMES.post_detail_reset_comment_list, reset);
  event_emitter.on(
    EVENT_NAMES.post_detail_comment_list_scroll_into_view,
    onScrollIntoCommentAreaView,
  );
});

onUnmounted(() => {
  event_emitter.off(EVENT_NAMES.post_detail_reset_comment_list, reset);
  event_emitter.off(
    EVENT_NAMES.post_detail_comment_list_scroll_into_view,
    onScrollIntoCommentAreaView,
  );
});
</script>

<style lang="scss" scoped></style>
