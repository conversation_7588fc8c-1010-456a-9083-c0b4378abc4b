<template>
  <div class="w-full">
    <Tabs v-model="activeTab" :default-value="activeTab" class="w-full">
      <TabsList class="w-full justify-start px-[12px] h-[48px] bg-[var(--op-fill-white)]">
        <TabsTrigger value="twitter" class="h-[28px] flex items-center flex-1">
          <div
            :class="[
              `w-full h-full mr-[10px] border-[1px] rounded-[2px] flex items-center justify-center`,
              activeTab == 'twitter'
                ? 'border-[color:var(--brand-1)] bg-[color:var(--brand-2)] dark:bg-[color:var(--brand-1-20)]'
                : 'border-[color:var(--line-1)]',
            ]"
          >
            <SvgIcon
              name="icon-x"
              :color="activeTab == 'twitter' ? 'var(--brand-1)' : 'var(--text-3)'"
              class="w-[16px] h-[16px]"
            ></SvgIcon>
          </div>
        </TabsTrigger>
        <TabsTrigger value="youtube" class="h-[28px] flex items-center flex-1">
          <div
            :class="[
              `w-full h-full border-[1px] rounded-[2px] flex items-center justify-center`,
              activeTab == 'youtube'
                ? 'border-[color:var(--brand-1)] bg-[color:var(--brand-2)] dark:bg-[color:var(--brand-1-20)]'
                : 'border-[color:var(--line-1)]',
            ]"
          >
            <SvgIcon
              name="icon-youtube2"
              :color="activeTab == 'youtube' ? 'var(--brand-1)' : 'var(--text-3)'"
              class="w-[20px] h-[14x]"
            ></SvgIcon>
          </div>
        </TabsTrigger>
      </TabsList>

      <TabsContent value="twitter" class="w-full" tabindex="" :force-mount="true">
        <Loading v-if="showLoading" />
        <div ref="contentRef" class="w-full h-[calc(100vh_-_140px)]"></div>
        <!-- <iframe class="w-full h-[calc(100vh_-_140px)]" :src="twitterIframe" />-->
      </TabsContent>

      <TabsContent value="youtube" class="w-full" tabindex="">
        <InfiniteScroll
          :back_to_top_visible="false"
          :finished_visible="false"
          :loading="postLoading"
          :finished="false"
          class="w-full relative mt-[12px]"
          @load-more="getMore"
        >
          <div class="mx-[12px] mt-[8px]">
            <div v-for="(item, index) in postList" :key="index" class="mb-[12px] last-of-type:mb-0">
              <CardItem :item="item"></CardItem>
            </div>
            <NoData
              v-if="postList.length === 0 && !postLoading"
              :is_atmosphere_anniversary="is_atmosphere_anniversary"
            ></NoData>
          </div>
        </InfiniteScroll>
      </TabsContent>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import SvgIcon from "@/components/common/svg-icon.vue";
import CardItem from "@/components/home/<USER>/index.vue";
import Loading from "@/components/common/loading.vue";
import { TWITTER_LANGUAGES, TWITTER_EVENT_HOST2 } from "@/configs/const";
import { getStandardizedLang } from "packages/utils/standard";
import { InfiniteScroll } from "@/components/common/scroll";
import { useHomeEventStore } from "@/store/home/<USER>";
import NoData from "@/components/common/nodata.vue";
import { report } from "packages/utils/tlog";
import { getIframeEl } from "@/utils/patch";
import { useAnniversary } from "@/store/home/<USER>";

const { getPostList } = useHomeEventStore();
const { is_atmosphere_anniversary } = storeToRefs(useAnniversary());
const { postList, postLoading, pageInfo } = storeToRefs(useHomeEventStore());

const getMore = () => {
  if (pageInfo.value.is_finish) return;
  getPostList(true);
};

const activeTab = ref("twitter");
const { IS_DARK_MODE } = window;

const twitterIframe = computed(() => {
  let lang = TWITTER_LANGUAGES[getStandardizedLang()] || "en";
  return `${TWITTER_EVENT_HOST2}/NIKKE_${lang}?theme=${IS_DARK_MODE ? "dark" : "light"}`;
});

const contentRef = ref();
const showLoading = ref(false);

watch(
  () => activeTab.value,
  (val: string) => {
    if (val === "twitter") {
      showLoading.value = true;
      setTimeout(() => {
        const iframeEle = getIframeEl(twitterIframe.value);
        iframeEle.className += " w-full h-[calc(100vh_-_140px)]";
        contentRef.value.appendChild(iframeEle);
        // 模拟2秒的loading
        setTimeout(() => {
          showLoading.value = false;
        }, 2000);
      });

      report.standalonesite_twitter_btn.cm_click();
    }
    if (val === "youtube") {
      report.standalonesite_youtube_btn.cm_click();
      contentRef.value.innerHTML = "";
    }
  },
  { immediate: true },
);

onMounted(() => {
  getPostList();
});
</script>
