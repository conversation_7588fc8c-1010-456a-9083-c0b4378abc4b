<template>
  <div class="w-full relative cursor-pointer">
    <Swiper
      v-if="list.length > 0"
      class="!w-full !h-[28px]"
      :loop="true"
      :index="0"
      direction="vertical"
      :autoplay="list.length > 1"
      :duration="5000"
      @change="onNewChange"
    >
      <div
        v-for="(item, index) in list"
        :key="index"
        class="w-full h-[28px] flex my-[1px] items-center pl-[5px] pr-[8px] relative z-[1] bg-[url('@/assets/imgs/home/<USER>')] bg-[length:100%_100%] news-item"
        :class="{
          'h-[30px] bg-[url(@/assets/imgs/atmosphere/bg-news.png)] bg-[length:100%_100%] ':
            is_atmosphere_anniversary,
        }"
        @click="goTo(item)"
      >
        <div v-if="item.new" class="w-[46px] flex-shrink-0 h-[18px] mr-[6px]">
          <img
            v-if="anniversary_visible"
            src="@/assets/imgs/year2-5/icon-new.png"
            class="w-full h-full new-img"
          />
          <img
            v-else-if="is_atmosphere_anniversary"
            src="@/assets/imgs/atmosphere/icon-new.png"
            class="w-full h-full new-img"
          />
          <img v-else src="@/assets/imgs/home/<USER>" class="w-full h-full" />
        </div>
        <div
          class="flex-1 line-clamp-1 text-[color:var(--text-3)] text-[length:12px] leading-[15px] mt-[3px] news-text"
        >
          {{ item.content }}
        </div>
        <div class="flex-shrink-0 flex items-center more-item">
          <span
            class="font-Abolition text-more text-[color:var(--text-3-60)] text-[length:var(--font-size-m)] mr-[2px] leading-[1]"
          >
            MORE
          </span>
          <SvgIcon
            name="icon-arrow-right3"
            class="w-[9px] h-[9px]"
            color="var(--text-3-60)"
          ></SvgIcon>
        </div>
      </div>
    </Swiper>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { Swiper } from "@/components/common/swiper/index.ts";
import SvgIcon from "@/components/common/svg-icon.vue";
import { Notice } from "packages/types/home";
import { report } from "packages/utils/tlog.ts";
// import { openUrlWithWhiteListQuery } from "@/utils/tools";
import { useWebCredential } from "@/composables/use-webcredential";

const props = defineProps<{
  list: Notice[];
  anniversary_visible?: boolean;
  is_two_and_half_anniversary?: boolean;
  is_atmosphere_anniversary?: boolean;
}>();

const curIndex = ref(0);

const { openUrlWithAuth } = useWebCredential();

const goTo = (item: Notice) => {
  report.standalonesite_newss_hint_item_click.cm_click({
    content_id: item.content,
    dst_url: item.link,
  });
  if (!item.link) return;
  // NOTE: 应运营要求改成当前窗口打开
  openUrlWithAuth(item.link, "_self");
};

const showReport = () => {
  const target = props.list[curIndex.value];
  report.standalonesite_newss_hint_item_expose.cm_vshow({
    content_id: target.content,
    dst_url: target.link,
  });
};

const onNewChange = (params: { current_index: number }) => {
  curIndex.value = params.current_index;
  showReport();
};

onMounted(() => {
  showReport();
});
</script>
<style lang="scss" scoped>
html[lang="en"] .text-more {
  margin-top: -3px;
}
</style>
