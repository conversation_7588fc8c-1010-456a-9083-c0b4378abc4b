<template>
  <div class="w-full">
    <div
      v-if="banner?.img"
      class="bg-[var(--op-fill-white)] px-[12px] pt-[8px] pb-[12px]"
      :class="{ 'con-z bg-transparent': anniversary_visible }"
    >
      <CommonImage
        v-if="banner?.img"
        :key="banner.img"
        :src="banner.img"
        alt=""
        class="w-full max-h-[400px] object-cover object-center"
        :class="[banner.href && `cursor-pointer`]"
        @click="onClickBanner(banner.href || '')"
      />
    </div>

    <div class="mt-[12px] mx-[15px]">
      <DropdownNormal
        :key="selected_task_id"
        :list="task_options"
        :active="selected_task_id"
        @change="(v: any) => onChangeTask(v.value as number)"
      >
        <template #trigger="{ item }">
          <SelectHead
            :text="item.name"
            :type="1"
            class="h-[28px] mr-[5px] max-w-[180px]"
            :anniversary_visible="anniversary_visible"
          />
        </template>
      </DropdownNormal>

      <DropdownNormal
        v-if="selected_task_id && selected_task_id !== -1"
        :key="selected_task_id + `` + selected_rank_id"
        :list="rank_options"
        :active="selected_rank_id || undefined"
        @change="(v: any) => onChangeRank(v.value as number)"
      >
        <template #trigger="{ item }">
          <SelectHead :text="item.name" :type="1" class="h-[28px] mr-[5px] max-w-[180px]" />
        </template>
      </DropdownNormal>
    </div>

    <InfiniteScroll
      :back_to_top_visible="false"
      :finished_visible="false"
      :loading="postLoading"
      :finished="false"
      :debounce_interval="10"
      class="w-full relative mt-[12px]"
      @load-more="getMore"
    >
      <div class="mt-[8px]" :class="is_atmosphere_anniversary ? '' : 'mx-[12px] '">
        <div v-for="(item, index) in postList" :key="index" class="mb-[12px] last-of-type:mb-0">
          <CardItem :item="item" :is_atmosphere_anniversary="is_atmosphere_anniversary">
            <template #task-rank>
              <div
                v-if="item.task_info"
                class="flex gap-[4px] items-center mt-[8px] text-[length:11px] text-[color:var(--text-3)] leading-[13px]"
              >
                <div
                  v-if="item.task_info?.task_id"
                  :class="[
                    isFilterValid({ task_id: item.task_info?.task_id }) &&
                      'text-[color:var(--brand-1)] cursor-pointer',
                  ]"
                  @click="onFilter({ task_id: item.task_info?.task_id })"
                >
                  #{{ item.task_info?.task_name }}
                </div>
                <div
                  v-if="item.task_info?.task_id && item.rank_info?.id"
                  :class="[
                    isFilterValid({
                      task_id: item.task_info?.task_id,
                      rank_id: item.rank_info?.id,
                    }) && 'text-[color:var(--brand-1)] cursor-pointer',
                  ]"
                  @click="
                    onFilter({ task_id: item.task_info?.task_id, rank_id: item.rank_info?.id })
                  "
                >
                  #{{ item.rank_info?.rank_name }}
                </div>
              </div>
            </template>
          </CardItem>
        </div>
        <NoData
          v-if="postList.length === 0 && !postLoading"
          :is_atmosphere_anniversary="is_atmosphere_anniversary"
        ></NoData>
      </div>
    </InfiniteScroll>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import DropdownNormal from "@/components/common/dropdown/index.vue";
import SelectHead from "@/components/common/select-head/index.vue";
import CardItem from "@/components/home/<USER>/index.vue";
import { InfiniteScroll } from "@/components/common/scroll";
import { useHomeCreatorHubStore } from "@/store/home/<USER>";
import { onMounted } from "vue";
import { CommonImage } from "@/components/common/image";
import NoData from "@/components/common/nodata.vue";
import { useWebCredential } from "@/composables/use-webcredential";
import { useAnniversary } from "@/store/home/<USER>";
import { useHomeSubPageLoadTlog } from "@/composables/use-home-tlog";
import { EVENT_NAMES, onEventEmitter } from "packages/utils/event-emitter";
import { updateUserStatusInPostList } from "@/utils/home";

const { anniversary_visible, is_atmosphere_anniversary } = storeToRefs(useAnniversary());

const { onChangeRank, onChangeTask, getPostList, onFilter, isFilterValid, refetchTasks } =
  useHomeCreatorHubStore();
const {
  postList,
  postLoading,
  selected_rank_id,
  selected_task_id,
  rank_options,
  task_options,
  banner,
  pageInfo,
} = storeToRefs(useHomeCreatorHubStore());

const { openUrlWithAuth } = useWebCredential();

const { reportSubPageLoadTime } = useHomeSubPageLoadTlog();

onMounted(() => {
  getPostList(false).then(() => {
    reportSubPageLoadTime();
  });
  refetchTasks();
});

const onClickBanner = (url: string) => {
  if (url) openUrlWithAuth(url, "_blank");
};

const getMore = () => {
  if (pageInfo.value.is_finish) return;
  getPostList(true);
};

onEventEmitter(EVENT_NAMES.user_status_change, (v) =>
  updateUserStatusInPostList(postList.value, v),
);
</script>

<style lang="scss" scoped></style>
