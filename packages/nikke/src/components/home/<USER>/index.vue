<template>
  <div class="w-full">
    <News
      v-if="noticeList.length"
      class="px-[15px] h-[55px] pt-[12px]"
      :list="noticeList"
      :anniversary_visible="anniversary_visible"
      :is_two_and_half_anniversary="is_two_and_half_anniversary"
      :is_atmosphere_anniversary="is_atmosphere_anniversary"
    ></News>

    <div
      class="mx-[15px]"
      :class="{ 'icon-ring': anniversary_visible || is_atmosphere_anniversary }"
    >
      <Tools
        v-if="kingList.length > 0"
        class="pb-[8px]"
        :class="{ 'pt-[12px]': !noticeList.length }"
        :list="kingList"
        :is_atmosphere_anniversary="is_atmosphere_anniversary"
      ></Tools>
    </div>

    <div
      class="flex justify-between items-center mx-[15px]"
      :class="is_atmosphere_anniversary ? '' : 'border-b-[1px] border-[var(--line-1)]'"
    ></div>

    <CreateEntrace
      :anniversary_visible="anniversary_visible"
      :is_two_and_half_anniversary="is_two_and_half_anniversary"
      :is_atmosphere_anniversary="is_atmosphere_anniversary"
      @compose="onCompose"
    />

    <InfiniteScroll
      :back_to_top_visible="false"
      :finished_visible="false"
      :loading="postLoading"
      :finished="false"
      :debounce_interval="10"
      class="w-full relative"
      @load-more="getMore"
    >
      <template v-if="showList.length > 0">
        <CardItem
          v-for="(item, index) in showList"
          :key="item.post_uuid"
          :item="item"
          :index="index"
          :plate_id="+activeId"
          :plate_name="activeKey"
          :need_expose="true"
          :is_atmosphere_anniversary="is_atmosphere_anniversary"
          @manage="onManage"
          @share="onShare"
          @star="onStar"
          @follow="onFollow"
          @detail="onDetail"
        />
      </template>
      <NoData
        v-if="showList.length === 0 && !postLoading"
        :is_atmosphere_anniversary="is_atmosphere_anniversary"
      ></NoData>
    </InfiniteScroll>

    <ManageDialog
      :visible="manage_visible"
      :can_edit="cur_item?.can_edit"
      @close="manage_visible = false"
      @delete="onDelete"
      @edit="onEditPost"
    />
  </div>
</template>

<script setup lang="ts">
import News from "@/components/home/<USER>/index.vue";
import Tools from "@/components/home/<USER>/index.vue";
import CardItem from "@/components/common/card-item/index.vue";
import ManageDialog from "@/components/common/manage-dialog/index.vue";
import NoData from "@/components/common/nodata.vue";
import CreateEntrace from "../createEntrance/index.vue";
import { useToast } from "@/components/ui/toast";

import { ref, onMounted, computed } from "vue";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { InfiniteScroll } from "@/components/common/scroll";
import { useHomeStore } from "@/store/home/<USER>";
import { useFollowUser } from "@/api/user";
import { useHomeRecommendStore } from "@/store/home/<USER>";
import { Routes } from "@/router/routes";
import { PostItem } from "packages/types/post";
import { postStar, useDeletePost, usePostForward } from "@/api/post";
import { LikeType, StanceType } from "packages/types/stance";
import { itemStatusAndCountHandler } from "packages/utils/tools";
import { t } from "@/locales";
import { useIsDeleted } from "@/composables/use-is-deleted";
import { useDialog } from "@/components/ui/dialog/index";
import { PlatId, PopCallbackValue } from "packages/types/common";
import { updatePostsData, updateUserStatusInPostList } from "@/utils/home";
import { usePostsStatus } from "@/composables/use-posts-status";
import { report } from "packages/utils/tlog";
import { getStandardizedLang } from "packages/utils/standard";

import { useAnniversary } from "@/store/home/<USER>";
import { useHomeSubPageLoadTlog } from "@/composables/use-home-tlog";
import { event_emitter, EVENT_NAMES, onEventEmitter } from "packages/utils/event-emitter";

// const title = ref(t("posts"));

const { show: showDialog } = useDialog();
const { filterIsDeleted, setIsDeletedValue } = useIsDeleted();
const { show: toast } = useToast();
const router = useRouter();
const { getPostList, getDistrictData, getTagData } = useHomeRecommendStore();

const { allNotices, activeId, activeKey } = storeToRefs(useHomeStore());
const { kingList, postList, postLoading, pageInfo } = storeToRefs(useHomeRecommendStore());
const { setPostData } = usePostsStatus();
const showList = computed(() => filterIsDeleted(postList.value));

const { anniversary_visible, is_two_and_half_anniversary, is_atmosphere_anniversary } =
  storeToRefs(useAnniversary());

const noticeList = computed(() => {
  const target = allNotices.value.find((item) => item.key === PlatId.recommend);
  return (target?.notices || []).slice(0, 3);
});

const onCompose = () => {
  report.standalonesite_news_publish_btn.cm_click();
  router.push({
    path: Routes.POST_COMPOSE,
    query: {
      plate_type: activeKey.value,
    },
  });
};

const { reportSubPageLoadTime } = useHomeSubPageLoadTlog();

// const onOrderChange = (val: OrderBy) => {
//   orderBy.value = val;
//   getPostList();
// };

const getMore = () => {
  if (pageInfo.value.is_finish) return;
  getPostList(true);
};

const cur_item = ref<PostItem>();

const onDetail = (item: PostItem) => {
  setPostData(item, postList.value);
};

const manage_visible = ref(false);
const onManage = (item: PostItem) => {
  cur_item.value = item;
  manage_visible.value = true;
};

const onShare = async (item: PostItem, channel_name: string, share_link: string) => {
  report.standalonesite_share_channel_btn.cm_click({
    channel_name,
    url: share_link,
  });

  const { forward_count } = await usePostForward.run({ post_uuid: item.post_uuid });
  item.forward_count = forward_count;
};

const onStar = async (item: PostItem) => {
  await postStar.run({
    post_uuid: item.post_uuid,
    type: StanceType.like,
    like_type: LikeType.like,
  });
  itemStatusAndCountHandler(item, "my_upvote.is_star", "upvote_count");
};

const onFollow = async (item: PostItem) => {
  const newStatus = await useFollowUser.run({ intl_openid: item.user.intl_openid });
  event_emitter.emit(EVENT_NAMES.user_status_change, {
    intl_openid: item.user.intl_openid,
    is_followed: newStatus.is_follow ? 1 : 0,
    is_mutual_follow: newStatus.is_mutual_follow ? 1 : 0,
  });
  updatePostsData(item, postList.value, newStatus);
};

const onDelete = async () => {
  showDialog({
    title: t("delete"),
    content: t("are_you_sure_to_delete"),
    confirm_text: t("confirm"),
    cancel_text: t("close"),
    async callback(options: { value: PopCallbackValue; close: () => void }) {
      const { value, close } = options;
      if (value === PopCallbackValue.confirm) {
        await useDeletePost.run({
          post_uuid: cur_item.value?.post_uuid || "",
        });
        manage_visible.value = false;
        toast({
          text: t("delete_successfully"),
          type: "success",
        });
        setIsDeletedValue(cur_item.value, true);
      }
      close();
    },
  });
};

// const onTagChange = (val: string) => {
//   activeTag.value = val;
//   getPostList();
// };

const onEditPost = () => {
  router.push({
    path: Routes.POST_COMPOSE,
    query: {
      post_uuid: cur_item.value?.post_uuid || ""!,
      edit_lang: getStandardizedLang(),
    },
  });
};

onEventEmitter(EVENT_NAMES.user_status_change, (v) =>
  updateUserStatusInPostList(postList.value, v),
);

onMounted(() => {
  getTagData();
  getDistrictData();
  getPostList().then(() => {
    reportSubPageLoadTime();
  });
});
</script>
