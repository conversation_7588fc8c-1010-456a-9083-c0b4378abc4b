<template>
  <div
    class="tag-list flex items-center overflow-y-auto overflow-x-auto"
    :class="{
      'no-scrollbar': is_mobile,
      'tag-list-anniversary': is_two_and_half_anniversary,
      'tag-list-atmosphere': is_atmosphere_anniversary,
    }"
  >
    <div
      v-for="(item, index) in list"
      :key="index"
      :class="[
        `px-[8px] py-[7px] bg-[color:var(--op-fill-white)] text-[length:12px] h-[26px] flex rounded-[4px] items-center flex-shrink-0 relative z-[1] cursor-pointer border-[0.5px] mr-[5px] last-of-type:mr-0 justify-center min-w-[90px]`,
        innerActiveId === item.id
          ? 'text-[var(--brand-1)] border-[color:var(--brand-1)] dark:bg-[color:var(--brand-1-20)] tag-select'
          : 'text-[color:var(--text-2)] border-[color:transparent]',
        is_two_and_half_anniversary && innerActiveId === item.id ? 'tag-item-ann  h-[29px]' : '',
        is_atmosphere_anniversary ? 'tag-item-normal' : '',
        is_atmosphere_anniversary && innerActiveId === item.id
          ? 'tag-item-atm  h-[28px]  text-[color:#94ECF2]'
          : '',
      ]"
      @click="triggerTag(item.id)"
    >
      #{{ item.tag_name }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Tag } from "packages/types/home";
import { useResponsive } from "@/composables/use-responsive";
import { storeToRefs } from "pinia";
import { useHomeStore } from "@/store/home/<USER>";
import { report } from "packages/utils/tlog";

const props = defineProps<{
  activeId: string;
  list: Tag[];
  anniversary_visible?: boolean;
  is_two_and_half_anniversary?: boolean;
  is_atmosphere_anniversary?: boolean;
}>();

const { activeId: plateId, activeKey } = storeToRefs(useHomeStore());
const { is_mobile } = useResponsive();

const emit = defineEmits(["change"]);

const innerActiveId = ref(props.activeId);

const triggerTag = (id: string) => {
  if (innerActiveId.value === id) {
    innerActiveId.value = "";
  } else {
    innerActiveId.value = id;
  }
  emit("change", innerActiveId.value);

  const tag = props.list.find((item) => item.id === id)!;
  report.standalonesite_tag_filter_btn.cm_click({
    tag_name: tag.tag_name,
    label_id: +plateId.value,
    label_name: activeKey.value,
  });
};
</script>

<style scoped lang="scss">
@-moz-document url-prefix() {
  .tag-list {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
  }
}
</style>
