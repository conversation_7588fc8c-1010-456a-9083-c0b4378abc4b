<template>
  <div
    class="p-[16px] pb-[12px] relative shadow-[0_0_10px_0_var(--op-shadow-black-5)] z-[1] overflow-hidden"
    :class="
      is_atmosphere_anniversary
        ? 'card-item border-b-[6px] border-[#22262D] '
        : 'bg-[color:var(--op-fill-white)]'
    "
  >
    <div
      class="px-[4px] text-[color:var(--text-1)] opacity-[0.7] text-[length:13px] leading-[16px] line-clamp-3"
      :class="is_atmosphere_anniversary ? 'card-item-username' : ''"
    >
      {{ item?.title }}
    </div>
    <div v-if="item?.ext_info || item?.pic_urls?.length" class="mt-[8px]">
      <Medias
        :key="item.ext_info + item.pic_urls?.join(',')"
        :img-list="item?.pic_urls"
        :ext_info="item.ext_info"
        :type="item.ext_info ? 'video' : 'image'"
        :original_url="item.original_url"
      ></Medias>
    </div>
    <!-- <div v-if="item?.content" class="mt-[8px] text-[var(--brand-1)] text-[length:11px] leading-[13px]">
      {{ item?.content }}
    </div> -->
    <slot name="task-rank"></slot>
    <div class="mt-[8px] flex items-center justify-between h-[24px]">
      <div
        v-if="item?.modified_on"
        class="text-[length:11px] leading-[13px]"
        :class="is_atmosphere_anniversary ? 'text-[color:#B6C8DD]' : 'text-[color:var(--text-3)]'"
      >
        {{ formatTime(item.created_on * 1000, t) }}
      </div>
      <div
        v-if="item?.original_url"
        class="flex items-center justify-end flex-1 cursor-pointer"
        @click="openLink(item?.original_url)"
      >
        <div
          class="text-[length:11px] leading-[13px]"
          :class="is_atmosphere_anniversary ? 'text-[color:#B6C8DD]' : 'text-[color:var(--text-3)]'"
        >
          {{ t("view_original_article") }}
        </div>
        <div class="w-[16px] h-[16px] ml-[6px]">
          <SvgIcon name="icon-arrow-right" color="var(--text-3)"></SvgIcon>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Medias from "@/components/common/medias/index.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import { PostItem } from "packages/types/post";
import { formatTime } from "@/utils/str";
import { useI18n } from "vue-i18n";
import { useWebCredential } from "@/composables/use-webcredential";
const { t } = useI18n();

defineProps<{
  item?: PostItem;
  is_atmosphere_anniversary?: boolean;
}>();

const { openUrlWithAuth } = useWebCredential();

const openLink = (url: string) => openUrlWithAuth(url, "_blank");
</script>

<style lang="scss" scoped></style>
