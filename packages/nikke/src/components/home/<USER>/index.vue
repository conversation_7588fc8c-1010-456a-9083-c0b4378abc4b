<template>
  <div class="w-full">
    <News
      v-if="noticeList.length"
      class="px-[15px] h-[55px] pt-[12px]"
      :list="noticeList"
      :anniversary_visible="anniversary_visible"
      :is_two_and_half_anniversary="is_two_and_half_anniversary"
      :is_atmosphere_anniversary="is_atmosphere_anniversary"
    ></News>

    <Header
      class="mt-[12px]"
      :show-region="false"
      :order-by="orderBy"
      :all-region="allRegion"
      :is_atmosphere_anniversary="is_atmosphere_anniversary"
      @order-change="onOrderChange"
      @region-change="onRegionChange"
    />
    <Tags
      class="mt-[8px] mx-[15px]"
      :list="tagList"
      :active-id="activeTag"
      :anniversary_visible="anniversary_visible"
      :is_two_and_half_anniversary="is_two_and_half_anniversary"
      :is_atmosphere_anniversary="is_atmosphere_anniversary"
      @change="onTagChange"
    />

    <CreateEntrace
      :anniversary_visible="anniversary_visible"
      :is_two_and_half_anniversary="is_two_and_half_anniversary"
      :is_atmosphere_anniversary="is_atmosphere_anniversary"
      @compose="onCompose"
    />

    <InfiniteScroll
      :back_to_top_visible="false"
      :finished_visible="false"
      :loading="postLoading"
      :finished="false"
      :debounce_interval="10"
      class="w-full relative"
      :class="is_atmosphere_anniversary ? '' : 'infinite-scroll-container'"
      @load-more="getMore"
    >
      <VirtualWaterfall
        :items="postList"
        :virtual="false"
        :calc-item-height="calcItemHeight"
        :row-key="'post_uuid'"
      >
        <template #default="{ item, index }">
          <ListItem
            :key="item.post_uuid"
            :index="index"
            :item="item"
            :need_expose="true"
            class="mb-[1px]"
            @star="onStar"
            @detail="onDetail"
            @collection="onCollection"
          />
        </template>
      </VirtualWaterfall>
      <NoData
        v-if="postList.length === 0 && !postLoading"
        :is_atmosphere_anniversary="is_atmosphere_anniversary"
      ></NoData>
    </InfiniteScroll>
  </div>
</template>

<script setup lang="ts">
import { computed, h, onMounted, render } from "vue";
import { storeToRefs } from "pinia";
import News from "@/components/home/<USER>/index.vue";
import Header from "@/components/home/<USER>/index.vue";
import Tags from "@/components/home/<USER>/index.vue";
import NoData from "@/components/common/nodata.vue";
import CreateEntrace from "../createEntrance/index.vue";
import { InfiniteScroll } from "@/components/common/scroll";
import ListItem from "@/components/home/<USER>/index.vue";
import { useHomeNikkeArtStore } from "@/store/home/<USER>";
import { useHomeStore } from "@/store/home/<USER>";
import { OrderBy, PostItem } from "packages/types/post";
import { postCollection, postStar } from "@/api/post";
import { Routes } from "@/router/routes";
import { LikeType, StanceType } from "packages/types/stance";
import { useRouter } from "vue-router";
import { itemStatusAndCountHandler } from "packages/utils/tools";
import { usePostsStatus } from "@/composables/use-posts-status";
import { report } from "packages/utils/tlog";
import { VirtualWaterfall } from "@lhlyu/vue-virtual-waterfall";
import { usePostItem } from "@/composables/use-post";
import { PlatId } from "packages/types/common";
import { useHomeSubPageLoadTlog } from "@/composables/use-home-tlog";
import { EVENT_NAMES, onEventEmitter } from "packages/utils/event-emitter";
import { updateUserStatusInPostList } from "@/utils/home";
import { useAnniversary } from "@/store/home/<USER>";

const { anniversary_visible, is_two_and_half_anniversary, is_atmosphere_anniversary } =
  storeToRefs(useAnniversary());

const router = useRouter();
const { allNotices, activeKey } = storeToRefs(useHomeStore());
const { getPostList, getTagData, resetPostList } = useHomeNikkeArtStore();
const { setPostData } = usePostsStatus();
const { tagList, activeTag, postList, postLoading, pageInfo, allRegion, orderBy } =
  storeToRefs(useHomeNikkeArtStore());

const noticeList = computed(() => {
  const target = allNotices.value.find((item) => item.key === PlatId.nikkeart);
  return (target?.notices || []).slice(0, 3);
});

const { reportSubPageLoadTime } = useHomeSubPageLoadTlog();

const calcItemHeight = (item: PostItem, item_real_width: number) => {
  let dom: HTMLDivElement | null = document.createElement("div");
  Object.assign(dom.style, {
    width: `${item_real_width}px`,
  });

  render(
    h(ListItem, {
      item: item,
    }),
    dom,
  );

  document.body.appendChild(dom);

  let height: number = dom.firstElementChild?.clientHeight || 0;
  const { getPostItemImageDisplaySize } = usePostItem();

  if (!height) {
    const display_size = getPostItemImageDisplaySize(item);
    const ratio = display_size.width / display_size.height;
    height = item_real_width / ratio;
  }

  // 移除
  document.body.removeChild(dom);
  dom = null;

  return height;
};

const onOrderChange = (val: OrderBy) => {
  orderBy.value = val;
  resetPostList();
  getPostList();
};

const onRegionChange = (val: boolean) => {
  allRegion.value = val;
  resetPostList();
  getPostList();

  report.standalonesite_all_region_btn.cm_click({ state: val ? 1 : 0 });
};

const onTagChange = (val: string) => {
  activeTag.value = val;
  resetPostList();
  getPostList();
};

const getMore = () => {
  if (pageInfo.value.is_finish) return;
  getPostList(true);
};

const onCompose = () => {
  report.standalonesite_news_publish_btn.cm_click();
  router.push({
    path: Routes.POST_COMPOSE,
    query: {
      plate_type: activeKey.value,
    },
  });
};

const onDetail = (item: PostItem) => {
  setPostData(item, postList.value);
};

const onStar = async (item: PostItem) => {
  await postStar.run({
    post_uuid: item.post_uuid,
    type: StanceType.like,
    like_type: LikeType.like,
  });
  itemStatusAndCountHandler(item, "my_upvote.is_star", "upvote_count");
};

const onCollection = async (item: PostItem) => {
  await postCollection.run({
    post_uuid: item.post_uuid,
  });
  item.is_collection = !item.is_collection;
};

onEventEmitter(EVENT_NAMES.user_status_change, (v) =>
  updateUserStatusInPostList(postList.value, v),
);

onMounted(() => {
  getTagData();
  getPostList().then(() => {
    reportSubPageLoadTime();
  });
});
</script>

<style lang="scss">
.infinite-scroll-container {
  background: linear-gradient(to bottom, transparent 200px, var(--fill-4) 300px);
}
</style>
