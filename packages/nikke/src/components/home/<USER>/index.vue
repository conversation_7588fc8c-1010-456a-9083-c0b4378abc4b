<template>
  <div
    v-click-interceptor.need_login.mute.sign_privacy.check_user_adult="goTo"
    class="fixed z-[50] bottom-[74px] left-[50%] w-[50px] cursor-pointer h-[58px] bg-[url('@/assets/imgs/common/comment-bubble.png')] bg-[length:100%_100%]"
    :style="{ transform: `translateX(${offset_left}px)` }"
    :class="{
      'post-bubble w-[120px] h-[150px]': is_two_and_half_anniversary,
      'post-bubble w-[64px] h-[71px]': is_atmosphere_anniversary,
    }"
  ></div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { useElementSize } from "@vueuse/core";
import { report } from "packages/utils/tlog.ts";

withDefaults(
  defineProps<{
    anniversary_visible?: boolean;
    is_two_and_half_anniversary?: boolean;
    is_atmosphere_anniversary?: boolean;
  }>(),
  {
    anniversary_visible: false,
    is_two_and_half_anniversary: false,
    is_atmosphere_anniversary: false,
  },
);

const emit = defineEmits(["compose"]);

const { width } = useElementSize(document.getElementById("layout-content"));

const offset_left = computed(() => {
  return width.value / 2 - 65;
});

const goTo = () => {
  report.standalonesite_posts_btn.cm_click();
  emit("compose");
};
</script>
