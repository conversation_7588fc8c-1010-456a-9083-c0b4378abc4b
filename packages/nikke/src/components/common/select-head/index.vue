<template>
  <div
    :class="[
      `relative flex items-center h-[20px] py-[4px] z-[1] cursor-pointer overflow-hidden`,
      (type === 0 || type === 1) && `px-[6px]`,
      type === 2 && `pl-[10px] pr-[3px]`,
      (type === 1 || type === 2) && `bg-[color:var(--op-fill-white)]`,
      disabled && `!bg-[var(--fill-2)] cursor-not-allowed`,
      border && 'border-[1px] border-[var(--line-1)]',
    ]"
    class="select-header"
  >
    <div
      :class="[
        `text-[length:var(--font-size-base)] leading-[1.2] text-left truncate flex-1 select-header-title`,
        type === 1 && `text-[color:var(--text-3)]`,
        type === 2 && `text-[color:var(--text-1)]`,
        (disabled || type === 0) && 'text-[color:var(--text-3-60)] font-Abolition',
      ]"
    >
      {{ text }}
    </div>

    <div
      v-if="type !== 0"
      :class="[
        `flex items-center justify-center`,
        type == 1 && `w-[12px] h-[24px] ml-[4px]`,
        type === 2 && `ml-[10px]`,
      ]"
    >
      <SvgIcon
        name="icon-arrow-down"
        :color="disabled ? 'var(--text-3)' : 'var(--text-1)'"
        class="w-[7px] h-[8px]"
      ></SvgIcon>
    </div>
    <div v-else class="ml-[3px] w-[12px] h-[11px]">
      <SvgIcon
        name="icon-arrow"
        color="var(--text-3-60)"
        :class="{ 'icon-color-ann': anniversary_visible || is_atmosphere_anniversary }"
      ></SvgIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";

withDefaults(
  defineProps<{
    border?: boolean;
    text?: string;
    disabled?: boolean;
    type?: 0 | 1 | 2 | 3 | 4 | 5 | 6; // 0: 特殊形状 1:默认形状
    anniversary_visible?: boolean;
    is_two_and_half_anniversary?: boolean;
    is_atmosphere_anniversary?: boolean;
  }>(),
  {
    text: "",
    type: 0,
    border: true,
    disabled: false,
  },
);
</script>
