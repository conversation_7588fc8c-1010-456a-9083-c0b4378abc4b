<template>
  <div
    :class="[
      `flex w-full max-w-[var(--max-pc-w)] items-center h-[44px] min-h-[44px] justify-center px-[12px] fixed top-0 bg-[length:100%_100%]`,
      bg ? `${bg}` : `bg-[url('@/assets/imgs/points/points-head-bg.png')]`,
      zIndex ? `z-[${zIndex}]` : `z-[50]`,
      bg !== 'bg-[transparent]' && bg ? 'border-b-[1px] border-[color:var(--line-1)] ' : '',
    ]"
  >
    <div
      v-if="!nogoBack"
      class="absolute left-[12px] top-[50%] -translate-y-[50%] w-[24px] h-[24px] cursor-pointer"
      @click="$emit('goback')"
    >
      <SvgIcon name="icon-goback" :color="color ? color : 'var(--color-white)'"></SvgIcon>
    </div>
    <div
      v-else
      class="absolute left-[7px] top-[50%] -translate-y-[50%] w-[24px] h-[24px] cursor-pointer"
      @click="$emit('close')"
    >
      <SvgIcon name="icon-pop-close" :color="color ? color : 'var(--color-white)'"></SvgIcon>
    </div>
    <div
      v-if="goHome"
      class="absolute left-[48px] top-[50%] -translate-y-[50%] w-[24px] h-[24px] cursor-pointer"
      @click="onClickHome"
    >
      <SvgIcon name="icon-home" :color="color ? color : 'var(--color-white)'"></SvgIcon>
    </div>
    <div
      v-if="title"
      :class="[
        `font-[Inter] text-[length:16px] font-bold leading-[19px] text-center`,
        color ? `text-[color:${color}]` : 'text-[color:var(--color-white)]',
      ]"
      @click="() => createViewer()"
    >
      {{ title }}
    </div>
    <!--do not use pointer at top: https://tapd.woa.com/tapd_fe/70114077/bug/detail/1070114077135606443 -->
    <div
      v-if="$slots.icon"
      class="absolute right-[12px] flex items-center top-[50%] -translate-y-[50%]"
    >
      <slot name="icon"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/common/svg-icon.vue";
import { RoutesName } from "@/router/routes";
import { useRouter } from "vue-router";
import { createViewer } from "@/components/common/img-viewer/index";

defineProps<{
  nogoBack?: boolean;
  title?: string;
  bg?: string;
  color?: string;
  zIndex?: number;
  goHome?: boolean;
}>();

const router = useRouter();

const onClickHome = () => {
  router.push({
    name: RoutesName.HOME,
  });
};

defineEmits(["goback", "textclick", "close"]);
</script>
