<template>
  <div
    :class="[
      `cursor-pointer max-w-max px-[8px] flex items-center justify-center relative z-[1]`,
      type_class,
      size_class,
      is_two_and_half_anniversary && 'anniversary-btn',
      is_atmosphere_anniversary && 'atmosphere-btn min-w-[88px] h-[25px]',
    ]"
    @click="handleClick"
  >
    <slot name="content"></slot>
    <span
      v-if="text"
      :class="[
        `flex items-center justify-center`,
        is_atmosphere_anniversary && 'atmosphere-btn-item',
      ]"
    >
      <slot name="icon"></slot>
      <span class="font-Abolition">{{ text }}</span>
    </span>

    <SvgIcon v-if="icon" :name="icon" :color="iconcolor" class="w-[16px] h-[16px]"></SvgIcon>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";

// utils
import { computed } from "vue";

const props = withDefaults(
  defineProps<{
    text?: string;
    icon?: string;
    iconcolor?: string;
    type?: "default" | "primary" | "disabled";
    size?: "s" | "m" | "l";
    mSmallFont?: boolean;
    anniversary_visible?: boolean;
    is_two_and_half_anniversary?: boolean;
    is_atmosphere_anniversary?: boolean;
  }>(),
  {
    text: "",
    icon: "",
    iconcolor: "",
    type: "default", // primary | default ,默认 default,
    size: "s",
    mSmallFont: false,
    anniversary_visible: false,
    is_two_and_half_anniversary: false,
    is_atmosphere_anniversary: false,
  },
);

const emit = defineEmits(["click"]);

const type_class = computed(() => {
  if (props.type === "default") {
    return `btn-default bg-[var(--op-fill-white)] text-[color:var(--text-1)] aa `;
  } else if (props.type === "primary") {
    return `btn-primary bg-[color:var(--brand-1)] text-[color:var(--color-white)]  bg-[url(@/assets/imgs/common/btns-bg.png)] bg-[length:100%_100%] bb`;
  } else if (props.type === "disabled") {
    return `btn-disabled bg-[color:var(--fill-2)] text-[color:var(--text-3-50)] cc`;
  } else {
    return "";
  }
});

const size_class = computed(() => {
  if (props.size === "s") {
    return `min-w-[64px] h-[25px] text-[length:var(--font-size-l)] leading-[14px] font-medium`;
  } else if (props.size === "m") {
    const font = props.mSmallFont
      ? "text-[length:var(--font-size-base)]"
      : "text-[length:var(--font-size-xl)]";
    return `min-w-[79px] h-[27px] font-normal ${font}`;
  } else {
    return "";
  }
});

const handleClick = (e: MouseEvent) => {
  if (props.type === "disabled") {
    e.stopPropagation();
    return;
  }

  emit("click", e);
};
</script>

<style lang="scss" scoped></style>
