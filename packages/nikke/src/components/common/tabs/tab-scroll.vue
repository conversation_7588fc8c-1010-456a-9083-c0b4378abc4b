<template>
  <Tabs
    ref="slide_tab_ref"
    default-value="all"
    class="w-full h-full min-h-[38px] border-b-[1px] bg-[var(--fill-3)] flex items-center tabs"
    :class="
      is_atmosphere_anniversary
        ? ' bg-[transparent]  border-[color:#336DCC] rounded-none '
        : ' border-[var(--line-1)]'
    "
    :wid="tab_container_width"
    :model-value="active_tab"
    @update:model-value="handleChangeTab"
  >
    <TabsList class="w-full h-full flex justify-start overflow-hidden mx-[15px]">
      <div
        ref="slide_tab_inner_ref"
        class="tab-scroll-container flex justify-between flex-nowrap text-nowrap w-full h-full overflow-x-auto overflow-y-hidden"
        :class="[is_mobile && 'no-scrollbar']"
      >
        <TabsTrigger
          v-for="tab in tabs"
          :key="tab.value"
          :value="tab.value"
          :disabled="is_loading"
          class="flex items-center justify-center h-full flex-shrink-0 box-border last-of-type:!mr-0"
          :class="{ 'mt-[4px]': is_atmosphere_anniversary }"
          :style="tab_style"
        >
          <div
            class="relative flex h-[20px] w-full justify-start items-center box-border cursor-pointer"
            :class="{ 'select-text': anniversary_visible }"
          >
            <!-- smFontSize 在此处异常-->
            <div
              class="z-10 relative h-full flex items-center justify-center text-[length:var(--font-size-langer)] pl-[7px]"
              :class="
                active_tab === tab.value
                  ? `text-[color:var(--text-1)] text-active aa`
                  : `text-[color:var(--text-3)] text-normal `
              "
            >
              <i
                v-if="active_tab === tab.value"
                class="w-[2px] h-[15px] bg-[var(--brand-1)] absolute left-0 top-1/2 -translate-y-1/2 tabs-line"
              ></i>
              <SvgIcon
                v-if="tab.icon"
                :name="tab.icon"
                :color="active_tab === tab.value ? 'var(--brand-1)' : 'var(--text-2)'"
                class="w-[12px] h-[12px]"
              ></SvgIcon>
              <div v-if="tab.key === 'creatorhub'" :key="tab.key">
                <img
                  v-if="is_two_and_half_anniversary"
                  :src="
                    active_tab === tab.value
                      ? creator_hub_ann_tab_active_img_ann
                      : creator_hub_ann_tab_img
                  "
                  class="h-[26px]"
                />

                <img
                  v-else-if="is_atmosphere_anniversary"
                  :src="
                    active_tab === tab.value
                      ? creator_hub_atm_tab_active_img
                      : creator_hub_atm_tab_img
                  "
                  class="h-[24px]"
                />

                <img
                  v-else
                  :src="active_tab === tab.value ? creator_hub_tab_active_img : creator_hub_tab_img"
                  class="h-[26px]"
                />
              </div>
              <!-- 非英文字体时，字体布局偏下方，需要修正 -->
              <span
                v-else
                class="font-Abolition !font-['Abolition'] tabs-text"
                :class="lang !== 'en' ? '-mt-[4px]' : ''"
                >{{ tab.label }}</span
              >
            </div>
          </div>
        </TabsTrigger>
      </div>
      <div
        class="flex-1 h-full bg-[var(--fill-2)] border-b-[1px] border-[var(--line-1)] tabs-border-color"
        :class="is_show_placeholder ? 'flex' : 'hidden'"
      ></div>
    </TabsList>
  </Tabs>
</template>
<script lang="ts" setup>
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ref, onMounted, computed } from "vue";
import { TabItem, TabsScrollEmits } from "packages/types/tabs";
import SvgIcon from "@/components/common/svg-icon.vue";
import creator_hub_tab_img from "@/assets/imgs/home/<USER>";
import creator_hub_tab_active_img from "@/assets/imgs/home/<USER>";
import creator_hub_ann_tab_img from "@/assets/imgs/year2-5/creatorhub_tab.png";
import creator_hub_ann_tab_active_img_ann from "@/assets/imgs/year2-5/creatorhub_tab-active.png";

import creator_hub_atm_tab_img from "@/assets/imgs/atmosphere/creatorhub_tab.png";
import creator_hub_atm_tab_active_img from "@/assets/imgs/atmosphere/creatorhub_tab-active.png";

import { getStandardizedLang } from "packages/utils/standard";
import { sleep } from "packages/utils/tools";
import { useResponsive } from "@/composables/use-responsive";
import { STANDARD_CMS_LANG_MAP } from "packages/configs/standard";

const lang = getStandardizedLang();

const props = withDefaults(
  defineProps<{
    loading?: boolean;
    active_id?: string | number;
    tabs: TabItem[];
    smFontSize?: boolean;
    anniversary_visible?: boolean;
    is_two_and_half_anniversary?: boolean;
    is_atmosphere_anniversary?: boolean;
  }>(),
  {},
);

const { is_mobile } = useResponsive();

const is_loading = computed(() => props.loading);

const emit = defineEmits<TabsScrollEmits>();

const active_tab = computed(() => {
  return props.active_id || props.tabs[0]?.value;
});

const slide_tab_inner_ref = ref<HTMLElement>();
const slide_tabs = ref<HTMLElement[]>([]);
const getTabsWidth = (arr: HTMLElement[]) => {
  return arr.reduce((acc, currentValue) => {
    return acc + currentValue.getBoundingClientRect().width;
  }, 0);
};

const tab_container_width = computed(() => {
  return slide_tab_inner_ref.value?.offsetWidth || 0;
});
const is_show_placeholder = computed(() => {
  return tab_container_width.value >= getTabsWidth(slide_tabs.value);
});

const handleChangeTab = async (e: string | number) => {
  // 点击自己则滚动到顶部后，再触发change事件
  if (e == active_tab.value) {
    scrollToView("smooth");
    await sleep(300);
    emit("change", e);
  } else {
    scrollToView("instant");
    emit("change", e);
  }
};

onMounted(() => {
  if (slide_tab_inner_ref.value?.children) {
    slide_tabs.value = Array.from(slide_tab_inner_ref.value?.children) as HTMLElement[];
  }
});

const scrollToView = (behavior: "smooth" | "instant") => {
  const el = document.querySelector("#layout-content")!;
  el.scrollTo({ top: 0, behavior });
};

const tab_style = computed(() => {
  if (props.tabs.length) {
    return {
      marginRight: lang === STANDARD_CMS_LANG_MAP.tw ? "14px" : "8px",
    };
  }
  return {};
});
</script>

<style lang="scss" scoped></style>
