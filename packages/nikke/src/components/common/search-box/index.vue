<template>
  <div
    class="common-btns relative flex items-center h-[27px] pr-[8px] pl-[6px] bg-[var(--fill-2)] border-[1px] border-[transparent] dark:border-[var(--line-1)] dark:bg-[var(--op-fill-white)] search-bg"
  >
    <i
      class="absolute -bottom-[3px] right-[2px] w-[1.2px] h-[11px] -z-[1] rotate-[45deg] dark:bg-[color:var(--line-1)]"
    ></i>

    <div class="w-[20px] h-[20px] cursor-pointer box-content" @click="showLanguageSetting">
      <div class="icon-language-origin">
        <SvgIcon name="icon-language" color="var(--text-1)"></SvgIcon>
      </div>
      <div
        class="icon-language-ann"
        :class="{ 'icon-language-atm': is_atmosphere_anniversary }"
      ></div>
    </div>

    <i class="w-[1px] h-[18px] bg-[#444444]/60 ml-[6px] mr-[4px] search-line"></i>
    <input
      ref="inputRef"
      v-model="keyword"
      type="text"
      class="flex-1 h-[20px] appearance-none bg-[transparent] bg-none m-0 p-0 text-[11px] leading-[20px] font-normal text-[color:var(--text-1)]"
      @focus="onFocus"
      @keyup.enter="onClickSearch"
    />
    <div
      class="w-[20px] h-[20px] cursor-pointer ml-[6px] p-[2px] search-icon"
      @click="onClickSearch"
    >
      <SvgIcon name="icon-search" color="var(--fill-1)"></SvgIcon>
    </div>
  </div>
</template>

<script setup lang="ts">
// cpnts
import SvgIcon from "@/components/common/svg-icon.vue";

// utils
import { ref, onMounted, watch } from "vue";
import { report } from "packages/utils/tlog.ts";
import { useDebounce } from "@vueuse/core";
import { showDialog } from "@/utils/dialog";
import LanguageSettingPopup from "@/components/setting/language/Popup.vue";

const props = withDefaults(
  defineProps<{
    keyword?: string;
    isFocus?: boolean;
    anniversary_visible?: boolean;
    is_two_and_half_anniversary?: boolean;
    is_atmosphere_anniversary?: boolean;
  }>(),
  {
    anniversary_visible: false,
    is_two_and_half_anniversary: false,
    is_atmosphere_anniversary: false,
  },
);
const emits = defineEmits(["clickSearch", "focus"]);

const keyword = ref(props.keyword || "");
// 点击搜索按钮
const onClickSearch = () => {
  emits("clickSearch", keyword.value);
  report.standalonesite_search_btn.cm_click({ content: keyword.value });
};
// 获得焦点
const onFocus = () => emits("focus");

// 当keyword为空时，立即清空搜索
watch(keyword, (val) => {
  if (!val) emits("clickSearch", val);
});

// 防抖搜索
const debounce_keyword = useDebounce(keyword, 800);
watch(debounce_keyword, (val) => {
  emits("clickSearch", val);
});

const inputRef = ref();
onMounted(() => {
  props.isFocus && inputRef.value.focus();
});

const showLanguageSetting = () => {
  showDialog(LanguageSettingPopup, {});
};

defineExpose({
  setSearchValue: (val: string) => {
    keyword.value = val;
  },
});
</script>

<style scoped></style>
