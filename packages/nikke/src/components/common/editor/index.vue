<script setup lang="ts">
// cpnts
import Loading from "@/components/common/loading.vue";
// import { useDialog } from "@/components/ui/dialog/index.ts";
// configs
import { EDITOR_EMOJI_IMAGE_HEIGHT, EDITOR_EMOJI_IMAGE_WIDTH } from "@/configs/const";
import { CONST_SUPPORTED_IMAGE_TYPES } from "packages/configs/const";
// quill
import { AttributeMap, Delta } from "quill/core";
import Emitter from "quill/core/emitter";
// extensions
import { ClipboardExtensioin, extendClipboardExtensionPrototype } from "./extensions/clipboard";
// utils
import Quill from "quill";
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";
import { delay, get } from "lodash-es";
import DOMPurify from "dompurify";
import { replaceNewLineWithBr } from "packages/utils/tools";
import { useCompositionEvent } from "@/composables/use-composition-event";
import { useImageUpload, ImageUploadErrorToastType } from "@/composables/use-image";
import logger from "packages/utils/logger";
import { onClickHyperLinkHandler } from "./utils";
import { useAegis } from "@/composables/use-aegis";
import { resovledHtmlImgQuality } from "@/composables/use-image";
import { useCDNConfigs } from "packages/utils/cdn";

const props = withDefaults(
  defineProps<{
    maxlen?: number;
    placeholder?: string;
    value?: string;
    disabled?: boolean;
    focus?: boolean;
    required?: boolean;
    disabled_pasted_image?: boolean;
    enable_tag?: boolean;
    thumbnail?: boolean;
    thumbnail_quality?: number;
  }>(),
  {
    value: "",
    disabled_pasted_image: false,
    enable_tag: false,
  },
);
const emits = defineEmits([
  "change",
  "focus",
  "blur",
  "update:value",
  "ready",
  "render",
  "tag",
  "click",
]);

const { getCDNConfigs } = useCDNConfigs();
const { logAndReportError } = useAegis();
const debug = logger("[editor:logger]");
const { composition, onAddCompositionsEvent, onRemoveCompositionsEvent } = useCompositionEvent();
const {
  uploading,
  getUploading,
  onCheckSizeLimit,
  onCheckFileType,
  onErrorToast,
  onUpload,
  toggleUploading,
} = useImageUpload();
const { image } = getCDNConfigs();

extendClipboardExtensionPrototype({
  toggleUploading,
  getUploading,
  disabled_pasted_image: props.disabled_pasted_image,
});

Quill.register("modules/clipboard", ClipboardExtensioin, true);

let quill: Quill;
// 如果使用 ref 的话，删除图片会出现怪异现象
// https://github.com/quilljs/quill/issues/3080
const editor = ref<HTMLElement>();
const text_len = ref(0);
const placeholder_visible = ref(true);
const scroll_to_bottom_timer = ref();
const thumbnail_quality = ref(props.thumbnail_quality || image?.thumbnail_quality || 1);

const proxy_value = computed(() => {
  // NOTE: 先总是展示原图
  if (!props.thumbnail || Boolean(1)) {
    return props.value;
  }

  return resovledHtmlImgQuality(props.value, thumbnail_quality.value);
});

const getTextLen = () => quill.getLength() - 1;

const scrollToBottom = (interval?: number) => {
  if (scroll_to_bottom_timer.value) {
    clearTimeout(scroll_to_bottom_timer.value);
  }

  scroll_to_bottom_timer.value = setTimeout(() => {
    // const root = quill.root;
    // if (root) {
    //   root.scrollTo({
    //     top: root.scrollHeight,
    //     behavior: "smooth",
    //   });
    // }
    quill.focus();
  }, interval || 1000);
};

// const insertText = (text: string) => {
//   const range = quill.getSelection(true) || 0;
//   quill.insertText(range.index, text);
// };

const insertHtml = (html: string, focus: boolean | undefined = !props.disabled) => {
  /**
   * @link https://stackoverflow.com/questions/46626633/how-do-you-insert-html-into-a-quilljs
   */
  const sanitize_html = DOMPurify.sanitize(replaceNewLineWithBr(html));
  if (props.disabled) {
    if (!editor.value) {
      throw new Error("editor is not defined");
    }
    editor.value.innerHTML = sanitize_html;
    return;
  }

  const delta = quill.clipboard.convert({ html: sanitize_html });
  quill.setContents(delta, Quill.sources.SILENT);

  if (focus) {
    const range = quill.getSelection(true) || 0;
    quill.setSelection(range.index + sanitize_html.length, Emitter.sources.USER);

    placeholder_visible.value = !sanitize_html;
    nextTick(() => {
      text_len.value = getTextLen();
    });
  }
};

const insertImage = (url: string, type?: "inline" | "block") => {
  if (props.disabled) {
    throw new Error("cannot insert image when disabled");
  }

  if (!url) {
    debug.warn(`[insertImage] url is empty`);
    return;
  }

  const getRange = () => quill.getSelection(true);
  const getAttributes = () =>
    type === "block"
      ? {
          alt: "",
          width: "",
          height: "auto",
        }
      : {
          alt: "",
          width: EDITOR_EMOJI_IMAGE_WIDTH,
          height: EDITOR_EMOJI_IMAGE_HEIGHT,
        };

  let range = getRange();

  const images: Array<{ arg: string | Record<string, unknown>; attributes?: AttributeMap | null }> =
    [
      {
        arg: { image: url },
        attributes: getAttributes(),
      },
    ];

  if (type === "block") {
    images.unshift({
      arg: "\n",
      attributes: {},
    });
  }

  const update = images.reduce(
    (delta: Delta, image) => {
      /**
       * @link https://github.com/slab/delta?tab=readme-ov-file#insert-operation
       * @linkn https://github.com/slab/quill/blob/b213e1073bac1478649f26e3c0dad50ad0eb2a49/packages/quill/src/modules/uploader.ts#L70
       */
      return delta.insert(image?.arg!, image?.attributes!);
    },
    new Delta()
      // keep the pre content
      .retain(range.index),
  ) as Delta;

  quill.updateContents(update, Emitter.sources.USER);

  const index = range.index + images.length;

  if (type === "block") {
    quill.insertText(index + 1, "\n", Quill.sources.USER);
    quill.insertText(index + 2, "\n", Quill.sources.USER);
    quill.setSelection(index + 3, Emitter.sources.USER);
  } else {
    quill.setSelection(index + 1, Emitter.sources.USER);
  }

  scrollToBottom(1000);
};

const insertLink = (text: string, url: string) => {
  if (props.disabled) {
    throw new Error("cannot insert link when disabled");
  }

  const range = quill.getSelection(true);
  /**
   * @link https://github.com/slab/quill/issues/3243#issuecomment-892133913
   */
  quill.insertText(range.index, text, "link", url);
};

const onSelectionChangeHandler = (range: { index: Number; length: Number }) => {
  if (!range) {
    emits("blur", quill);
  } else {
    if (placeholder_visible.value && getTextLen()) {
      placeholder_visible.value = false;
    }
    emits("focus", quill);
  }
};

const onTagIntercept = (delta: Delta, _old_delta: Delta, _source: string) => {
  const { ops } = delta;
  const insert = ops.find((op) => op.insert);

  if (insert?.insert === "#") {
    onManualregisterBlur();
    emits("tag", quill, delta);
  }
};

const onTextChangeHandler = (delta: Delta, old_delta: Delta, source: string) => {
  props.enable_tag && onTagIntercept(delta, old_delta, source);

  if (props.maxlen && quill.getLength() > props.maxlen) {
    quill?.deleteText(props.maxlen, quill?.getLength());
  }

  text_len.value = getTextLen();
  placeholder_visible.value = !text_len.value;

  const text = text_len.value
    ? get(quill, "container.firstChild.innerHTML", "")
    : // 清空的情况下还是会有类似 <div><br/></div> 的效果
      "";

  emits("update:value", text);
  emits("change", text);
};

const onManualregisterBlur = () => {
  quill.setSelection(null);
};

/*
 * @description upload image for copying image from clipboard
 * @link https://github.com/slab/quill/blob/b213e1073bac1478649f26e3c0dad50ad0eb2a49/packages/quill/src/modules/uploader.ts#L57
 * @param   {File[]}  files   [files description]
 * @return  {[]}              [return description]
 */
const onUploaderHandler = async (files: File[]) => {
  if (uploading.value) {
    return;
  }

  let url = "";
  const file = files?.[0];
  if (file) {
    const { size, type } = file;
    if (!onCheckSizeLimit(size)) {
      return onErrorToast(ImageUploadErrorToastType.exceed_size_limit);
    }
    if (!onCheckFileType(type)) {
      return onErrorToast(ImageUploadErrorToastType.file_type_not_support);
    }
    uploading.value = true;
    try {
      url = (await onUpload(file)) || "";
      //  insertImage(url, "block");
    } catch (error: any) {
      logAndReportError(error.msg, error);
      onErrorToast(ImageUploadErrorToastType.file_upload_failed);
    } finally {
      uploading.value = false;
    }
  }

  return url;
};

const onClick = (e: Event) => {
  const target = e.target as HTMLElement;
  if (target.tagName === "A") {
    onClickHyperLinkHandler(e);
  }

  if (props.disabled) {
    return;
  }

  if (!quill.isEnabled()) {
    quill.enable(true);
    quill.focus();
  }

  emits("click", e);
};

onMounted(() => {
  if (props.disabled) {
    // ready hook
    emits("ready", editor.value);
    nextTick(() => {
      // render hook
      emits("render", editor.value);
    });
    return;
  }

  quill = new Quill(editor.value as HTMLElement, {
    modules: {
      toolbar: false,
      uploader: {
        mimetypes: CONST_SUPPORTED_IMAGE_TYPES,
        async handler(_range: Range, files: File[]) {
          if (props.disabled_pasted_image) {
            return;
          }
          const url = (await onUploaderHandler(files)) || "";
          insertImage(url, "block");
        },
      },
    },
    placeholder: "",
    theme: "snow",
    debug: import.meta.env.DEV && false,
  });

  onAddCompositionsEvent(editor.value as HTMLElement);
  // ready hook
  emits("ready", quill);
  quill.on("text-change", onTextChangeHandler);
  quill.on("selection-change", onSelectionChangeHandler);
  /**
   * @link https://github.com/slab/quill/issues/1397#issuecomment-372720610
   */
  quill.root.addEventListener("blur", onManualregisterBlur);

  nextTick(() => {
    insertHtml(proxy_value.value);
    nextTick(() => {
      // render hook
      emits("render", quill);
    });
    if (props.disabled) {
      quill.disable();
      return;
    }
    delay(() => {
      if (props.focus) {
        quill.focus();
        quill.scrollSelectionIntoView();
      }
    }, 500);
  });
});

onBeforeUnmount(() => {
  if (props.disabled) {
    return;
  }

  onRemoveCompositionsEvent(editor.value as HTMLElement);
  quill.off("text-change", onTextChangeHandler);
  quill.off("selection-change", onSelectionChangeHandler);
  quill.root.removeEventListener("blur", onManualregisterBlur);
});

defineExpose({
  quill: computed(() => quill || {}),
  insertImage,
  insertLink,
  insertHtml,
});

/////////// watch ///////////
watch(
  () => composition.value,
  () => {
    placeholder_visible.value = composition.value
      ? //
        false
      : !getTextLen();
  },
);
</script>

<!-- NOTE: disabled 的情况下使用普通的 div 元素，因为 quill 在使用 chrome 翻译的时候会有问题 -->
<template>
  <div class="relative flex flex-col" @click="onClick">
    <template v-if="disabled">
      <div
        ref="editor"
        class="text-[13px] ql-editor"
        v-html="DOMPurify.sanitize(replaceNewLineWithBr(proxy_value))"
      ></div>
    </template>

    <template v-else>
      <div
        v-if="uploading"
        class="absolute inset-0 flex justify-center items-center z-10 bg-[var(--color-white)] dark:bg-[var(--color-black)]"
      >
        <Loading></Loading>
      </div>

      <div ref="editor" class="overflow-y-auto notranslate"></div>

      <div
        v-if="placeholder && placeholder_visible"
        class="absolute top-0 text-[12px] flex items-center z-0"
        @click="() => quill.focus()"
      >
        <span class="text-[color:var(--text-3-60)]">{{ placeholder }}</span>
        <span v-if="required" class="text-[color:var(--error)] ml-[1px]">*</span>
      </div>

      <div
        v-if="maxlen"
        class="flex w-full relative h-[30px] items-center justify-end text-[9px] leading-[11px] pointer-events-none"
      >
        <span class="text-[color:var(--brand-1)]">{{ text_len }}</span>
        <span class="mx-[2px] text-[color:var(--text-3)]">/</span>
        <span class="text-[color:var(--text-3)]">{{ maxlen }}</span>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
@mixin no-scrollbar {
  &::-webkit-scrollbar {
    height: 0;
    width: 0;
  }
  &::-webkit-scrollbar-thumb {
    background-color: transparent;
  }
}

:deep(.ql-editor) {
  user-select: auto !important;
  padding: 0;
  color: var(--text-1);

  @include no-scrollbar;

  &.ql-blank::before {
    font-style: normal;
    left: 0;
    font-family: "Lato";
    padding: 0;
    color: var(--text-3);
  }

  .ql-bg-white {
    background-color: var(--op-fill-white);
  }

  .ql-color-black {
    color: var(--text-1);
  }

  .ql-video {
    max-width: 300px;
  }

  .ql-img-inline {
    display: inline-block;
    height: 60px;
    width: 60px;
    margin: 10px 0 0 10px;
  }

  .ql-img-block {
    display: block;
    margin: 10px 0 0 10px;
  }

  img {
    display: inline-block;
    border-radius: 8px;
  }

  a {
    text-decoration: none;
    padding-left: 14px;
    position: relative;
    display: inline-flex;
    align-items: center;
    color: var(--brand-1);
    cursor: pointer;

    &::before {
      content: "";
      height: 12px;
      width: 12px;
      position: absolute;
      left: 0;
      background-image: url("@/assets/imgs/logo/icon-copy-link.png");
      background-size: contain;
      background-repeat: no-repeat;
    }
  }
}
:deep(.ql-container.ql-snow),
:deep(.ql-toolbar.ql-snow) {
  border: none;
}
</style>
