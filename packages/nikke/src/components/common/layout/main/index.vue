<script setup lang="ts">
// cpnts
import VerticalScreenPrompt from "@/components/vertical-screen-prompt/index.vue";
import Loading from "@/components/common/loading.vue";

// utils
import { computed, ref } from "vue";
import { isMobileDevice } from "packages/utils/tools";
import { get } from "lodash-es";
import { useRoute } from "vue-router";
import { RoutesName } from "@/router/routes";
import { useAnniversary } from "@/store/home/<USER>";
import { storeToRefs } from "pinia";

const {
  // anniversary_visible,
  is_two_and_half_anniversary,
  is_atmosphere_anniversary,
  anniversary_bg_config,
} = storeToRefs(useAnniversary());
const is_mobile = isMobileDevice();
const route = useRoute();

const layout_content_ref = ref();

const start_y = ref(0);
const pull_distance = ref(0);
const threshold = 50;
const loading_visible = ref(false);

const wrapper_style = computed(() => {
  const ret = {};
  if (anniversary_bg_config.value.bg_image) {
    return {
      ...ret,
      backgroundImage: `url(${anniversary_bg_config.value.bg_image})`,
      backgroundColor: anniversary_bg_config.value.bg_color,
    };
  }
  return ret;
});

const container_class = computed(() => {
  const ret = [
    `flex flex-col h-screen w-screen max-w-[var(--max-pc-w)] relative bg-[var(--fill-3)] transform scale(1)`,
  ];

  if (![RoutesName.HOME].includes(route.name as RoutesName)) {
    return ret; //.concat("bg-[var(--fill-3)]");
  }

  if (is_atmosphere_anniversary.value) {
    return ret.concat(
      `atmosphere`, //bg-[url('@/assets/imgs/atmosphere/tabs-bd-bg.png')] bg-[length:100%_100%]`,
    );
  }

  if (is_two_and_half_anniversary.value) {
    return ret.concat(`year2-5`);
  }

  return ret;
});

const isEnablePull = () => {
  if (!is_mobile) {
    return false;
  }

  if (![RoutesName.HOME].includes(route.name as RoutesName)) {
    return false;
  }

  if (layout_content_ref.value.scrollTop >= 0) {
    return false;
  }

  return true;
};

function onTouchStart(e: TouchEvent) {
  if (!isEnablePull()) return;

  start_y.value = get(e, "touches[0].clientY", 0);
  pull_distance.value = 0;
}

function onTouchMove(e: TouchEvent) {
  if (!isEnablePull()) {
    loading_visible.value = false;
    return;
  }

  const current_y = get(e, "touches[0].clientY", 0);
  let distance = current_y - start_y.value;

  // console.log("[onTouchMove] distance", distance);

  if (distance > 0) {
    e.preventDefault();
    // 阻尼效果，拉动距离减半
    distance = distance / 2;
    pull_distance.value = distance;
  }

  if (distance >= threshold) {
    loading_visible.value = true;
    pull_distance.value = threshold;
  }
}

function onTouchEnd() {
  if (loading_visible.value) {
    setTimeout(() => {
      window.location.reload();
    }, 100);
  }

  // reset
  pull_distance.value = 0;
  loading_visible.value = false;
}
</script>

<template>
  <div
    :class="[
      `flex justify-center w-full h-full overflow-hidden bg-no-repeat bg-[position:top_center] bg-fixed bg-[100%_auto]`,
    ]"
    :style="wrapper_style"
    @touchstart="onTouchStart"
    @touchmove="onTouchMove"
    @touchend="onTouchEnd"
  >
    <div :class="container_class">
      <div v-show="loading_visible" class="fixed flex justify-center w-full mx-auto z-10">
        <Loading class="w-[20px] h-[20px]" />
      </div>

      <div
        id="layout-content"
        ref="layout_content_ref"
        :class="[
          `overflow-y-auto max-w-[var(--max-pc-w)] w-full relative no-scrollbar`,
          is_mobile && `pb-[100px]`,
        ]"
      >
        <slot></slot>
      </div>
      <VerticalScreenPrompt></VerticalScreenPrompt>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
