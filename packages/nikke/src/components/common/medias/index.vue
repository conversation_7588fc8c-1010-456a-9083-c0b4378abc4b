<template>
  <div class="relative z-[1] cursor-pointer" @click="onBoxClick">
    <template v-if="type === 'video' && parsed_ext_info?.platform">
      <div
        v-if="[Platform.youtube, Platform.youtubeshort].includes(parsed_ext_info.platform)"
        ref="ytVideoRef"
        class="w-full rounded-[4px] overflow-hidden"
        :style="{ height: `${vHeight}px` }"
      >
        <YoutubePlayer :vid="parsed_ext_info.video_id" @play="() => emits('play')"></YoutubePlayer>
      </div>

      <TiktokPlayer
        v-else-if="parsed_ext_info.platform === Platform.tiktok"
        :vid="parsed_ext_info.video_id"
      />
      <VideoPlayer
        v-else-if="is_valid_mp4(parsed_ext_info.video_url)"
        :video_url="parsed_ext_info.video_url"
        video_type="mp4"
        :cover_url="parsed_ext_info.video_cover"
      />
      <TwitterPlayer
        v-else-if="parsed_ext_info.platform === Platform.twitter"
        :vid="parsed_ext_info.video_id"
        :cover_url="parsed_ext_info.video_preview_url"
        :original_url="original_url"
      />
    </template>

    <div v-if="type === 'image'">
      <div class="w-full flex relative z-[1]" @click="onImageClick">
        <template v-for="url in proxy_img_list.slice(0, 3)" :key="url">
          <template v-if="proxy_img_list.length === 1">
            <div class="w-full min-h-[100px] max-h-[250px] rounded-[4px] overflow-hidden relative">
              <CommonImage
                class="object-cover h-[100%] rounded-[4px]"
                image_class="object-cover h-[100%] rounded-[4px]"
                alt=""
                :src="url"
                :thumbnail="true"
                :auto_dethumbnail="false"
                :thumbnail_quality="is_mobile ? 0 : 30"
                :loading="true"
              />
            </div>
          </template>
          <template v-if="proxy_img_list.length === 2">
            <div
              class="flex-1 mr-[5px] last-of-type:mr-0 max-h-[250px] min-h-[130px] rounded-[4px] overflow-hidden relative"
            >
              <CommonImage
                class="w-full h-full object-cover"
                image_class="w-full h-full object-cover"
                alt=""
                :src="url"
                :thumbnail="true"
                :auto_dethumbnail="false"
                :thumbnail_quality="is_mobile ? 0 : 30"
                :loading="true"
              />
            </div>
          </template>
          <template v-if="proxy_img_list.length >= 3">
            <div
              class="flex-1 h-[103px] mr-[5px] last-of-type:mr-0 rounded-[4px] overflow-hidden relative"
            >
              <CommonImage
                class="w-full h-full object-cover"
                image_class="w-full h-full object-cover"
                alt=""
                :src="url"
                :thumbnail="true"
                :auto_dethumbnail="false"
                :thumbnail_quality="is_mobile ? 0 : 30"
                :loading="true"
              />
            </div>
          </template>
        </template>
      </div>

      <template v-if="proxy_img_list.length > 3">
        <MorePicNum :num="proxy_img_list.length"></MorePicNum>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import {
  YoutubePlayer,
  TiktokPlayer,
  VideoPlayer,
  TwitterPlayer,
} from "@/components/common/video/player/index";
import MorePicNum from "@/components/common/more-pic-num/index.vue";
import { CommonImage } from "@/components/common/image/index";
import { useElementSize } from "@vueuse/core";
import { Platform } from "packages/types/common";
import { safeParse } from "packages/utils/tools";
import { isMobileDevice } from "packages/utils/tools";
import { createViewer } from "@/components/common/img-viewer/index";

const props = defineProps<{
  /** 原文跳转地址 */
  original_url?: string;
  ext_info?: any;
  type: "image" | "video";
  imgList: string[];
}>();

const is_mobile = isMobileDevice();

const proxy_img_list = computed(() => props.imgList.filter(Boolean));
const parsed_ext_info = computed(() => {
  const pei = safeParse<any[]>(props.ext_info);
  return pei?.[0] || pei || null;
});

const emits = defineEmits(["play"]);

const onImageClick = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target) {
    createViewer({
      image_list: proxy_img_list.value,
      target,
    });
  }
};

const is_valid_mp4 = (url: string) => {
  try {
    const { pathname } = new URL(url);
    return pathname.endsWith(".mp4");
  } catch (e) {
    return false;
  }
};

const ytVideoRef = ref();
const { width } = useElementSize(ytVideoRef);
/** 存储上一次的宽度，避免在 keep-alive activate 时，一瞬间宽度为 0 导致布局异常 */
const last_width = ref(0);

watch(width, (new_width) => {
  if (new_width > 0) last_width.value = new_width;
});

// 视频宽高比：16:9
const vHeight = computed(() => {
  return (last_width.value * 9) / 16;
});

// 只有点击图片区域的时候，在停止冒泡
const onBoxClick = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (target.tagName === "IMG") {
    e.stopPropagation();
  }
};
</script>
