<template>
  <div
    class="min-h-screen pb-[30px]"
    :class="[
      `bg-[url('@/assets/imgs/user-center/bg-banner.png')] bg-[length:100%_auto] bg-no-repeat bg-[position:0_-44px]`,
      show_float_follow ? `!pb-[80px]` : ``,
    ]"
  >
    <Head v-if="false" bg="bg-[transparent]" :go-home="true" @goback="router.back()">
      <template #icon>
        <SvgIcon
          v-if="is_self"
          name="icon-setting"
          class="w-[24px] h-[24px] cursor-pointer"
          @click="router.push('/setting')"
        ></SvgIcon>
      </template>
    </Head>
    <div
      v-else
      class="flex w-full max-w-[var(--max-pc-w)] items-center h-[44px] justify-left px-[12px] fixed top-0 bg-[length:100%_100%] z-50 transition-all duration-300 border-b-[1px] gap-[12px]"
      :class="{
        'bg-[color:var(--op-fill-white)] dark:border-[color:var(--line-1)] border-transparent':
          show_float_user,
        'bg-transparent border-transparent': !show_float_user,
      }"
    >
      <div class="w-[24px] h-[24px] cursor-pointer" @click="router.back()">
        <SvgIcon
          name="icon-goback"
          :color="show_float_user ? 'var(--text-1)' : 'var(--color-white)'"
        ></SvgIcon>
      </div>
      <div class="w-[24px] h-[24px] cursor-pointer" @click="router.push(Routes.HOME)">
        <SvgIcon
          name="icon-home"
          :color="show_float_user ? 'var(--text-1)' : 'var(--color-white)'"
        ></SvgIcon>
      </div>
      <div
        class="flex items-center transition-opacity duration-300 gap-[12px]"
        :class="{ 'opacity-0': !show_float_user }"
      >
        <div class="rounded-full w-[28px] h-[28px] cursor-pointer">
          <Avatar
            :src="user_info?.avatar || ``"
            :auth_type="user_info?.auth_type"
            :frame="user_info?.avatar_pendant"
          />
        </div>
        <div class="text-[length:13px] leading-[16px] text-[color:var(--text-1)]">
          {{ user_info?.username }}
        </div>
      </div>
      <div class="flex items-center ml-auto">
        <!-- 评论挂件配置入口，下个迭代启用 -->
        <SvgIcon
          v-if="is_self"
          name="icon-personalized"
          class="w-[24px] h-[24px] cursor-pointer mr-[12px]"
          @click="router.push(Routes.USER_PERSONALIZED)"
        ></SvgIcon>
        <SvgIcon
          v-if="is_self"
          name="icon-setting"
          class="w-[24px] h-[24px] cursor-pointer"
          :color="show_float_user ? 'var(--text-1)' : 'var(--color-white)'"
          @click="router.push('/setting')"
        ></SvgIcon>
        <SvgIcon
          v-else-if="is_login"
          name="icon-more"
          class="w-[24px] h-[24px] cursor-pointer rotate-90"
          :color="'var(--color-white)'"
          @click="handleMore"
        ></SvgIcon>
      </div>
    </div>

    <InfoHead></InfoHead>

    <UserInfos
      v-if="is_show_game_card && (is_self || player_info?.has_saved_role_info)"
      class="relative z-[5]"
    ></UserInfos>

    <div class="bg-[var(--fill-3)] rounded-[16px_16px_0_0] mt-[15px] min-h-[50vh]">
      <div v-if="is_self && is_login && is_user_page" class="mx-[15px] pt-[15px]">
        <UserPoints></UserPoints>
      </div>

      <div v-if="is_self" class="px-[13px] mt-[16px] mb-[10px]">
        <CreatorhubEntry />
      </div>

      <Tabs v-model="activeTab" :default-value="activeTab" class="w-full min-h-[30vh]">
        <TabsList
          class="w-full justify-start h-[46px] border-b-[1px] border-[var(--line-1)] px-[15px]"
        >
          <TabsTrigger
            v-for="tab in tabs"
            :key="tab.value"
            :value="tab.value"
            class="flex items-center justify-center h-full flex-grow flex-shrink-0 box-border pr-[5px] last-of-type:pr-0"
            @click="onClickTab(tab)"
          >
            <div
              class="relative flex h-[20px] justify-start items-center w-full box-border cursor-pointer"
            >
              <div
                class="z-10 relative h-full flex items-center justify-center text-[length:var(--font-size-langer)]"
                :class="
                  activeTab === tab.value
                    ? `text-[color:var(--text-1)]`
                    : `text-[color:var(--text-3)]`
                "
              >
                <i
                  class="w-[2px] h-[15px] bg-[var(--brand-1)] mr-[7px]"
                  :class="[activeTab === tab.value ? 'opacity-100' : 'opacity-0']"
                ></i>
                <span class="font-Abolition leading-[15px]">{{ tab.label }}</span>
              </div>
            </div>
          </TabsTrigger>
        </TabsList>

        <TabsContent
          v-for="(item, index) in tabs"
          :key="index"
          :value="item?.value"
          class="w-full"
          tabindex=""
        >
          <component
            :is="item.component"
            v-if="is_show(item.privacy_key)"
            :key="tabs_content_refresh_flag"
          ></component>
          <NoData v-else-if="!privacy_loading"></NoData>
        </TabsContent>
      </Tabs>
    </div>
    <transition :css="false" @leave="(_el: Element, done: any) => motions.custom?.leave?.(done)">
      <div
        v-if="show_float_follow"
        v-motion="'custom'"
        class="fixed bottom-0 left-0 w-full z-10"
        :initial="{ y: !direction ? 0 : 60 }"
        :enter="{ y: 0, transition: { stiffness: 60 } }"
        :leave="{ y: 60, transition: { stiffness: 60 } }"
      >
        <div class="max-w-[var(--max-pc-w)] mx-auto pb-8 px-6 box-border">
          <button
            v-click-interceptor.need_login.mute.sign_privacy.stop="onToggleFollow"
            class="w-full h-[40px] flex items-center justify-center bg-[var(--brand-1)] text-[length:14px] text-[color:var(--color-white)] font-bold"
            :class="{
              '!bg-[var(--fill-7)] !text-[color:var(--text-2)] border-[1px] border-[var(--line-1)]':
                current_user_followed || user_info?.is_black,
            }"
          >
            <SvgIcon
              v-if="!current_user_followed && !user_info?.is_black"
              name="icon-add"
              :color="!current_user_followed ? 'var(--color-white)' : 'var(--text-2)'"
              class="w-[16px] h-[16px] mr-[4px]"
            ></SvgIcon>
            {{
              user_info?.is_black
                ? t("blocking")
                : current_user_followed
                  ? t("is_following")
                  : t("follow")
            }}
          </button>
        </div>
      </div>
    </transition>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, watch, computed, onActivated, nextTick, onDeactivated } from "vue";
import { useI18n } from "vue-i18n";
import Head from "@/components/common/head/index.vue";
import NoData from "@/components/common/nodata.vue";
import SvgIcon from "@/components/common/svg-icon.vue";
import InfoHead from "@/components/user-center/info-head/index.vue";
import UserInfos from "@/components/user-center/user-info/index.vue";
import UserPoints from "@/components/user-center/user-points/index.vue";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Posts from "@/components/user-center/posts/index.vue";
import Comments from "@/components/user-center/comments/index.vue";
import Favorites from "@/components/user-center/favorites/index.vue";

import { Routes, RoutesName } from "@/router/routes";
import { useRoute, useRouter } from "vue-router";
import { useUser } from "@/store/user";
import { useUserCenter } from "@/composables/use-user-center";
import { ShowType } from "packages/types/setting";
import { storeToRefs } from "pinia";
import { report } from "packages/utils/tlog";
import { useMotions } from "@vueuse/motion";
import Avatar from "@/components/common/avatar/index.vue";
import { useDialog } from "@/components/ui/dialog";
import { PopCallbackValue } from "packages/types/common";
import { useFollowUser } from "@/api/user";
import { useHomeOutPostStore } from "@/store/home/<USER>";
import CreatorhubEntry from "@/components/creatorhub/creatorhub-entry.vue";
import { useUserBlock } from "@/composables/use-user-block";
import { showDialog } from "@/utils/dialog";
import ManagePopup from "@/components/user-center/manange-popup/index.vue";
import { useScrollAction } from "@/composables/use-scroll";
import { event_emitter, EVENT_NAMES } from "packages/utils/event-emitter";

defineOptions({
  name: RoutesName.USER,
});

const route = useRoute();
const { t } = useI18n();
const { is_login } = storeToRefs(useUser());
const router = useRouter();
const is_user_page = computed(() => route.name === RoutesName.USER);
// const user_info = ref<UserInfo>();
// const privacy_settings = ref<PrivacySettings>();
const activeTab = ref("my_posts");
// const is_show_game_card = ref(false);
const { user_info, is_self, is_show, player_info, privacy_loading, refetchUserInfo, intl_openid } =
  useUserCenter();
const tabs = computed<
  {
    value: string;
    privacy_key: keyof ShowType;
    label: string;
    component: any;
  }[]
>(() => [
  {
    value: "my_posts",
    privacy_key: "show_my_posts",
    label: is_self.value ? t("my_posts") : t("his_posts"),
    component: Posts,
  },
  {
    value: "my_comments",
    privacy_key: "show_my_comment", // TODO 需要换
    label: is_self.value ? t("my_comments") : t("his_comments"),
    component: Comments,
  },
  {
    value: "my_favorites",
    privacy_key: "show_my_collection",
    label: is_self.value ? t("my_favorites") : t("his_favorites"),
    component: Favorites,
  },
]);

const is_show_game_card = computed(() => is_show("show_my_game_card"));

onMounted(() => {
  refetchUserInfo();
});
onActivated(async () => {
  registerScroll();
  refetchUserInfo();
  if (route.meta.needRefresh) {
    await nextTick();
    activeTab.value = "my_posts";
    tabs_content_refresh_flag.value++;
  }
});

onDeactivated(() => {
  unRegisterScroll();
});

const handleMore = () => {
  if (!user_info.value) return;
  showDialog(ManagePopup, {
    user_info: user_info.value,
    onBlockChange: (_is_black) => {
      refetchUserInfo();
    },
  });
};

watch(is_login, (val) => {
  console.log("user_store.is_login", val);
});
if (is_self.value && !is_login.value) {
  router.push(Routes.HOME);
}
onMounted(() => {});

// 发起上报
watch(
  () => [is_login.value, intl_openid.value, is_self.value],
  () => {
    if (is_self.value && !is_login.value) return;
    if (intl_openid.value) {
      report.standalonesite_usercenter_page.cm_vshow({ openid: intl_openid.value! });
    }
  },
  { immediate: true },
);

const motions = useMotions();

const { scroll_top, direction, registerScroll, unRegisterScroll } = useScrollAction();

const show_float_follow = computed(() => {
  if (is_self.value) return false;
  if (!is_login.value) return true; // 注意：未登录且地址栏有 intl_openid时，展示关注按钮
  if (!user_info.value) return false;
  return current_user_followed.value ? direction.value !== "down" : true;
});
const show_float_user = computed(() => {
  return scroll_top.value > 160;
});
/** 当前用户是否已关注 */
const current_user_followed = computed(() => {
  return user_info.value?.is_followed === 1;
});
const { show: showConfirmDialog } = useDialog();
const { mutateAsync: follow } = useFollowUser({
  onSuccess: (data) => {
    event_emitter.emit(EVENT_NAMES.user_status_change, {
      intl_openid: intl_openid.value!,
      is_followed: data.is_follow ? 1 : 0,
      is_mutual_follow: data.is_mutual_follow ? 1 : 0,
    });
  },
});
const { handleUnblock } = useUserBlock();

const onToggleFollow = async () => {
  if (is_self.value || !intl_openid.value) return;
  if (user_info.value?.is_black) {
    const changed = await handleUnblock(intl_openid.value);
    if (changed) refetchUserInfo();
    return;
  }
  if (current_user_followed.value) {
    showConfirmDialog({
      title: t("unfollowed"),
      content: t("are_you_sure_to_unfollow"),
      confirm_text: t("keep_follow"),
      cancel_text: t("unfollow"),
      async callback(options: { value: PopCallbackValue; close: () => void }) {
        const { value, close } = options;
        if (value === PopCallbackValue.cancel) {
          await follow({ intl_openid: intl_openid.value! });
          refreshOutPostListIfNeed();
        }
        close();
      },
    });
  } else {
    await follow({ intl_openid: intl_openid.value });
    refreshOutPostListIfNeed();
  }
};

const tabs_content_refresh_flag = ref(1);
const outpost = useHomeOutPostStore();
/** 刷新缓存的post列表，避免其中的用户信息关注状态不一致; 刷新底部的tab内容 */
const refreshOutPostListIfNeed = () => {
  if (outpost.postList.some((item) => item.user.intl_openid === intl_openid.value)) {
    outpost.getPostList();
  }
  tabs_content_refresh_flag.value++;
};

const onClickTab = (tab: { value: string; label: string }) => {
  // 上报曝光
  report.standalonesite_usercenter_sub_tab.cm_click({ tab_name: tab.value });
};
</script>

<style scoped lang="scss"></style>
