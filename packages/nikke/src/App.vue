<script setup lang="ts">
// cpnts
import Layout from "@/components/common/layout/main/index.vue";
import CookieBannerEntrance from "@/components/common/cookiebanner/entrance.vue";
import Loading from "@/components/common/loading.vue";

// configs
import { ENV_TEST } from "packages/configs/env";

// utils
import { useUser } from "@/store/user";
import { useUserStore as useShiftysUser } from "@/shiftyspad/stores/user";
import { getLoginSource, isInGame, LoginSource } from "packages/utils/tools";
import { KEEP_ALIVE_NAMES, Routes } from "@/router/routes.ts";
import { useInterceptor } from "./composables/use-interceptor";
import { useMissionStore } from "./store/mission";
import { computed, ref, defineAsyncComponent, onMounted } from "vue";
import { useAegis } from "@/composables/use-aegis";
import { report } from "packages/utils/tlog";
import { useAppWheel } from "./composables/use-scroll";

const ServiceWorkerRefreshPrompt = defineAsyncComponent(
  () => import("@/components/common/service-workder-refresh-prompt.vue"),
);
const MultipleVersion = defineAsyncComponent(
  () => import("@/components/common/multiple-version/index.vue"),
);

const { onAppWheelAction } = useAppWheel();
const { logAndReportError } = useAegis();
const { initRoleInfo } = useShiftysUser();
const user_store = useUser();
const login_source = getLoginSource();

const page_init_loading = ref(true);
const is_service_worker_enable = ref([ENV_TEST].includes(import.meta.env.MODE));

console.log(`Is Service Worker Enable: `, is_service_worker_enable.value);

const ingameRoleCheck = async () => {
  const user_role = await initRoleInfo();
  if (user_role.is_rebinded) {
    location.reload();
  }
};

const check = async () => {
  if (login_source !== LoginSource.NORMAL) {
    try {
      // 游戏相关登录
      const loginMethod =
        login_source === LoginSource.IN_GAME
          ? user_store.loginInGame
          : user_store.loginFromWebCredential;
      await loginMethod();
    } catch (error: any) {
      logAndReportError(error.msg, error);
    } finally {
      page_init_loading.value = false;
    }

    const interceptor = async () => {
      await user_store.checkLogin();
      if (isInGame()) {
        ingameRoleCheck();
      }
      await user_store.refetchUserInfo();
      const { afterLoginInterceptor } = useInterceptor();
      await afterLoginInterceptor();
    };

    interceptor();
    return;
  }

  // 普通网页登录
  user_store.checkLogin();
};

check();
// 注册任务中心状态，内部会自动判断完成相关任务
useMissionStore();
// 注册滚动事件
onAppWheelAction();

const page_loading_visible = computed(() => {
  if (login_source === LoginSource.NORMAL) {
    return false;
  }
  return page_init_loading.value;
});

report.standalonesite_cm_init.cm_vshow();

/** 页面如果缓存，是否需要根据 fullPath 来刷新 */
const shouldRefreshByFullPath = (routePath: string) => {
  return [Routes.USER, Routes.POST_DETAIL].includes(routePath as Routes);
};

onMounted(() => {
  // 计算页面加载时长
  const page_loaded_time = Math.floor(performance.now());
  console.log(`App Loaded Time: ${page_loaded_time}ms`);
  report.standalonesite_home_page_loading.cm_vshow({ du: page_loaded_time });
});
</script>

<template>
  <Layout>
    <template v-if="page_loading_visible">
      <div class="flex h-full w-full justify-center items-center">
        <Loading></Loading>
      </div>
    </template>

    <template v-else>
      <router-view v-slot="{ Component, route }">
        <keep-alive :include="KEEP_ALIVE_NAMES" :max="15">
          <component
            :is="Component"
            v-if="shouldRefreshByFullPath(route.path)"
            :key="route.fullPath"
          ></component>
          <component :is="Component" v-else :key="route.path"></component>
        </keep-alive>
      </router-view>
    </template>

    <CookieBannerEntrance></CookieBannerEntrance>

    <template v-if="is_service_worker_enable">
      <component :is="ServiceWorkerRefreshPrompt" />
      <component :is="MultipleVersion" />
    </template>
  </Layout>
</template>
