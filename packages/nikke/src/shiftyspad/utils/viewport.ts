import { debounce } from "lodash-es";
import { isMobileDevice } from "packages/utils/tools";

export default (_app: any) => {
  function setFont() {
    const html = document.documentElement;
    const body = document.body;
    if (!isMobileDevice()) {
      const ww = html.clientWidth || window.innerWidth;
      // ww = Math.min(1920, ww)
      // let _rem = 16;
      // if (ww <= 1440) {
      //   _rem = Math.max(ww, 1280) / 1920 * 16;
      // }
      // let _rem = Math.max(ww, 1280) / 1920 * 16;
      let _rem = (Math.min(ww, 480) / 750) * 16;
      // 不缩放
      // const _rem = 16
      if (ww <= 900) {
        body.classList.add("is-mobile");
        body.classList.remove("is-pc");
      } else {
        // body.classList.remove("is-mobile");
        // body.classList.add("is-pc");
        body.classList.add("is-mobile");
        body.classList.remove("is-pc");
      }
      html.style.setProperty("--scale", String(_rem / 16));
      html.style.fontSize = _rem + "px";
    } else {
      const k = 750;
      // const _rem = (480 / k) * 16;
      const _rem = (Math.min(html.clientWidth, 480) / k) * 16;
      // console.log("viewport_adapt", _rem);
      html.style.setProperty("--scale", String(_rem / 16));
      html.style.fontSize = _rem + "px";
      body.classList.remove("is-pc");
      body.classList.add("is-mobile");
    }
  }
  function setFontLater() {
    setTimeout(function () {
      setFont();
    }, 300);
  }
  function resetFont() {
    setFont();
    setFontLater();
  }
  resetFont();
  document.addEventListener("DOMContentLoaded", resetFont, false);
  window.addEventListener("resize", debounce(resetFont, 300), false);
  window.addEventListener("load", resetFont, false);
};
