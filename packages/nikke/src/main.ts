import install from "@/boot/pwa-install";
import "@/shiftyspad/service/rum";

import { createApp } from "vue";
import { isWebp } from "@/shiftyspad/const";
import viewport from "@/shiftyspad/utils/viewport";
import { useMain } from "@/composables/use-main.ts";
import { useDirectives } from "@/directives";
import { checkSearchMultipleQuestionMark, format } from "packages/utils/qs";
import { getStandardizedLang } from "packages/utils/standard";
import logger from "packages/utils/logger";
import { urlSearchObjectify } from "packages/utils/qs";
import { get } from "lodash-es";

import "@/assets/css/tailwind.css";
import "@/assets/css/variables.css";
import "@/assets/css/common.css";
import "./assets/css/year2-5.scss";
import "./assets/css/atmosphereSB.scss";

import App from "./App.vue";

import { useCDNConfigs } from "packages/utils/cdn";
import { isMobileDevice } from "packages/utils/tools";
import { useLang } from "./composables/use-lang";
import { useVconsole } from "@/composables/use-vconsole";

import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";

dayjs.extend(utc);

const { loadVConsole } = useVconsole();
const { setAppLang } = useLang();
const { installAppPlugins } = useMain();
const { loadCDNConfigs } = useCDNConfigs();

(async () => {
  await loadCDNConfigs(import.meta.env.MODE);

  if (checkSearchMultipleQuestionMark()) {
    location.search = format(location.search);
  }

  get(urlSearchObjectify(), "is_log") && logger.level("log");

  const url_lang = getStandardizedLang();

  url_lang && setAppLang(url_lang);

  const app = createApp(App);

  if (isWebp) {
    document.body.classList.add("webp");
  }

  installAppPlugins(app, { is_main_app: true }).mount("#app");

  viewport(app);
  useDirectives(app);

  setTimeout(() => {
    // 移动端才弹出安装弹窗
    isMobileDevice() && install.prompt();
  }, 5000);

  setTimeout(() => {
    import("quill/dist/quill.core.css");
    import("quill/dist/quill.snow.css");

    loadVConsole();
  }, 0);
})();
