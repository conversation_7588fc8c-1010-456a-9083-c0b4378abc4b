.atmosphere {
    .nav-bg {
        background-image: url("@/assets/imgs/atmosphere/nav-bg.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }

    .logo-bg {
        background-image: url("@/assets/imgs/atmosphere/app-logo2.png");
        background-size: 100% 100%;
    }

    .search-bg {
        background-color: #697A96;
    }

    .search-line {
        background-color: #fff;
    }

    .search-icon {
        .fill-current {
            color: #fff !important;
        }
    }

    .icon-language-origin {
        display: none;
    }

    .icon-language-atm {
        width: 100%;
        height: 100%;
        background-image: url("@/assets/imgs/atmosphere/icon-language.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }

    .icon-notification {
        width: 100%;
        height: 100%;
        background-image: url("@/assets/imgs/atmosphere/icon-notification.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }

    .icon-phone {
        width: 100%;
        height: 100%;
        background-image: url("@/assets/imgs/atmosphere/icon-phone.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }

    // .tabs
    .tabs-content {
        min-height: calc(100vh - 236px);
        position: relative;
        // border-radius: 30px 30px 0 0;
        display: flex;
        flex-direction: column;

        &-hd {
            &.fixed-nav {
                .tabs {
                    border-radius: 0 0 0 0;
                    //   background-color: #2b6bd7;
                    background-image: url("@/assets/imgs/atmosphere/tabs-hd-fixed.png");
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                }
            }


            .tabs {
                width: 100%;
                background-image: url("@/assets/imgs/atmosphere/tabs-hd-bg.png");
                background-repeat: no-repeat;
                background-size: 100% 100%;
                background-color: transparent;
            }


            .select-text {
                // color: #f00;
            }

            .text-active {
                background: linear-gradient(to bottom, #FFFFFF 14%, #C1DCFC);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                color: #C1DCFC;
            }

            .text-normal {
                color: rgba($color: #B4D7FF, $alpha: 1);
            }

            .tabs-line {
                background-color: #15D3CC;
            }

            .tabs-border-color {
                // border-color: #79a3f6;
            }
        }

        .tabs-text {
            // color: #fff;
        }

        &-bd {
            width: 100%;
            height: 100%;
            position: relative;
            // background-color: #f1f1f1;
            flex: 1;


            &.bg-img {
                // width: 100%;
                // background-image: url("@/assets/imgs/atmosphere/tabs-bd-bg.png");
                // background-position: center top;
                // background-size: 100% auto;
                // background-repeat: no-repeat;
                // background-attachment: fixed;

            }

            &.fixed-background {
                background-attachment: fixed;
                // background-position: center calc(30px + 50%);
            }
        }

        .fixed-bg {
            display: none;
        }
    }

    // new
    // .news-item {
    //     background-image: url("@/assets/imgs/atmosphere/bg-news.png");
    //     background-size: 100% 100%;
    //     height: 30px;

    //     .new-img {
    //         object-fit: cover;
    //     }

    //     .news-text {
    //         color: #3b65c9;
    //     }

    //     .text-more {
    //         color: #3a64c8;
    //     }

    //     .more-item {
    //         .fill-current {
    //             color: #3a64c8 !important;
    //         }
    //     }
    // }


    // header
    .header {
        // border-color: rgba($color: #c2deff, $alpha: 0.4);
        position: relative;
        z-index: 3;
        margin-top: 0;
        height: 48px;
        padding-top: 12px;
        box-sizing: border-box;

        &::before {
            width: 100%;
            height: 4px;
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            background-image: url('@/assets/imgs/atmosphere/header-line.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }

        .icon-post {
            background-image: url("@/assets/imgs/atmosphere/icon-post.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }

        .text-post {
            background: linear-gradient(to bottom, #FFFFFF 14%, #C1DCFC);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .select-header {
            border: 1px solid transparent;

            &-title {
                color: rgba($color: #ffffff, $alpha: 0.6);
            }


        }

        .switch-text {
            color: rgba($color: #ffffff, $alpha: 0.6);
        }
    }

    .tag-list-atmosphere {


        .tag-item-normal {
            background-image: url("@/assets/imgs/atmosphere/tag-list.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            background-color: transparent;
            color: #B6C8DD;

            &.tag-item-atm {
                background-image: url("@/assets/imgs/atmosphere/tag-list-select.png");
                background-repeat: no-repeat;
                background-size: 100% 100%;
                background-color: transparent;
                box-sizing: border-box;
                color: #94ECF2;
            }
        }

        .tag-select {
            border-color: transparent;
        }
    }

    // .aaa {
    //     position: relative;

    //     &::before {
    //         content: '';
    //         position: absolute;
    //         bottom: 0;
    //         left: 0;
    //         width: 100%;
    //         height: 17px;
    //         background-image: url("@/assets/imgs/atmosphere/card-bottom.png");
    //         background-repeat: no-repeat;
    //         background-size: 100% 100%;
    //     }
    // }
    .text-tools {
        background: linear-gradient(to bottom, #FFFFFF, #9CFAFD);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .card-item {
        position: relative;

        &::before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            bottom: -2px;
            right: -5px;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #22262D;
            transform: rotate(-45deg);
        }

        &::after {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            bottom: -2px;
            left: -6px;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #22262D;
            transform: rotate(45deg);
        }

        &-username {
            color: #fff;
        }

        &-desc {
            color: #B6C8DD;
        }

        &-text {
            color: #B6C8DD;
        }

        &-line {
            width: 100%;
            // width: 345px;
            height: 4px;
            background-image: url("@/assets/imgs/atmosphere/card-item-line.png");
            background-repeat: no-repeat;
            background-size: 100% 100%;
            margin: 0 auto;
        }

        // border: none;
        // margin-bottom: 6px;
    }

    // btn
    .atmosphere-btn {

        &.btn-primary {
            width: 88px;
            height: 45px;
            background-image: url("@/assets/imgs/atmosphere/btns-bg.png");
            background-color: transparent;
        }

        // &.btn-default {
        //     background-image: url("@/assets/imgs/atmosphere/btn-bg.png");
        //     background-color: transparent;
        //     background-size: 100% 100%;
        // }

        // atmosphere-btn-item
        &-item {
            position: absolute;
            height: 25px;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            line-height: 25px;
            top: 5px;
            left: 0;

        }

        .font-Abolition {
            // font-size: 8px;
        }
    }

    .icon-plus {
        width: 13px;
        height: 13px;
        background-image: url("@/assets/imgs/atmosphere/icon-plus.png");
        background-size: 100% 100%;
    }

    .post-bubble {
        background-image: url("@/assets/imgs/atmosphere/post-bubble.png");
    }

    .icon-like {
        display: inline-block;
        background-image: url("@/assets/imgs/atmosphere/icon-like.svg");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        // width: 16px;
    }

    .icon-like-cur {
        display: inline-block;
        background-image: url("@/assets/imgs/atmosphere/icon-like-cur.svg");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        // width: 16px;
    }

    .icon-color-ann {
        color: rgba(255, 255, 255, 0.6) !important;
    }



}

@media (max-width: 750px) {
    .atmosphere {
        .tabs-content {
            &-bd {

                // &.bg-img {
                //     height: 1910px;
                //     background-image: url("@/assets/imgs/atmosphere/tabs-bd-bg.png");
                //     background-position: center top;
                //     background-size: auto 100%;
                //     background-repeat: no-repeat;
                // }
            }

            // .fixed-bg {
            //     position: fixed;
            //     top: 81px;
            //     left: 50%;
            //     width: 100%;
            //     max-width: 480px;
            //     display: block;
            //     transform: translateX(-50%);
            //     height: 542px;
            //     background-image: url("@/assets/imgs/year2-5/tabs-bd-bg1.png");
            //     background-position: center top;
            //     background-size: 100% 542px;
            //     background-repeat: no-repeat;
            //     z-index: 1;
            // }
        }
    }
}