/// <reference lib="webworker" />

// Give TypeScript the correct global.
declare var self: ServiceWorkerGlobalScope & {
  __WB_MANIFEST: any;
  __VUE_VITE_VERSIOIN__: string;
};

// utils
// import * as navigationPreload from "workbox-navigation-preload";
import { registerRoute } from "workbox-routing";
import { ExpirationPlugin } from "workbox-expiration";
import { CacheableResponsePlugin } from "workbox-cacheable-response";
import { StaleWhileRevalidate, Strategy, StrategyHandler } from "workbox-strategies";

// configs
import { ENV_DEVELOPMENT, ENV_TEST } from "packages/configs/env";
import {
  STORAGE_CS_PREFIX,
  STORAGE_CS_CSS,
  STORAGE_CS_FONT,
  STORAGE_CS_IMAGE,
  STORAGE_CS_JS,
} from "packages/configs/storage";
import { get } from "lodash-es";

const IS_CACHE_ASSETS_ENABLE = [ENV_TEST].includes(import.meta.env.MODE);
const IS_DEV = import.meta.env.MODE === ENV_DEVELOPMENT;

console.log(
  `Service Worker Version: ${import.meta.env.VITE_VERSIOIN}, Build Time: ${import.meta.env.VITE_APP_BUILD_TIME}`,
);

/** @link https://developer.chrome.com/docs/workbox/troubleshooting-and-logging */
// self.__WB_DISABLE_DEV_LOGS = true;

/**
 * @description Enable navigation preload
 * @see https://developer.chrome.com/docs/workbox/modules/workbox-navigation-preload
 * @link https://github.com/GoogleChrome/workbox/issues/2472
 */
// navigationPreload.enable();

// function isTypeFile(file_path: string, types: Array<string>) {
//   const extension = file_path.split(".").pop()?.toLowerCase();
//   return types.some((type) => type.toLocaleLowerCase() === extension);
// }

// self.__WB_MANIFEST is default injection point
// precacheAndRoute(self.__WB_MANIFEST);

/**
 * @link workbox/packages/workbox-precaching/src/utils/deleteOutdatedCaches.ts
 */
const deleteOutdatedCaches = async (): Promise<string[]> => {
  const cache_names = await self.caches.keys();

  const cache_names_to_delete = cache_names.filter((cache_name) => {
    return (
      cache_name.includes(STORAGE_CS_PREFIX) &&
      [STORAGE_CS_CSS, STORAGE_CS_FONT, STORAGE_CS_IMAGE, STORAGE_CS_JS].includes(cache_name)
    );
  });

  await Promise.all(cache_names_to_delete.map((cache_name) => self.caches.delete(cache_name)));

  return cache_names_to_delete;
};

/**
 * @link workbox/packages/workbox-precaching/src/cleanupOutdatedCaches.ts
 */
function cleanupOutdatedCaches(): void {
  // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-436484705
  self.addEventListener("activate", ((event: ExtendableEvent) => {
    event.waitUntil(
      deleteOutdatedCaches().then((cachesDeleted) => {
        if (process.env.NODE_ENV !== "production") {
          if (cachesDeleted.length > 0) {
            console.log(
              `The following out-of-date precaches were cleaned up ` + `automatically:`,
              cachesDeleted,
            );
          }
        }
      }),
    );
  }) as EventListener);
}

// clean old assets
cleanupOutdatedCaches();

self.addEventListener("install", () => {
  // self.skipWaiting();
  console.log("Service Worker installing...");
});

self.addEventListener("activate", () => {
  self.clients.claim();
  console.log("Service Worker activating...");
});

self.addEventListener("fetch", (event) => {
  const url = event.request.url;
  const is_blablalink_url = get(url.split("."), 1) === "blablalink";
  if (!is_blablalink_url) {
    return false;
  }
  // console.log("Fetching:", event.request.url);
});

// let allowlist: undefined | RegExp[];
// if (import.meta.env.DEV) allowlist = [/^\/$/];

// to allow work offline
// registerRoute(new NavigationRoute(createHandlerBoundToURL("index.html"), { allowlist }));

/**
 * @description custom strategy
 * @link https://developer.chrome.com/docs/workbox/modules/workbox-strategies#custom_strategies
 */
class MultipleHTMLVersionStrage extends Strategy {
  protected async _handle(
    request: Request,
    handler: StrategyHandler,
  ): Promise<Response | undefined> {
    const client_url = request.url;
    try {
      const fetch_url = this.buildNewUrl(client_url);
      const response = await handler.fetch(fetch_url);
      console.log("[MultipleHTMLVersionStrage] intercept html response: ", response);
      if (!response.ok) {
        throw new Error(
          `[MultipleHTMLVersionStrage] Network response was not ok, fetch_url: ${fetch_url}`,
        );
      }
      return response;
    } catch (error) {
      console.error(error);
      return await fetch(client_url);
    }
  }

  checkStringEmptyError(str: any) {
    if (!str) {
      throw new Error("Value is empty");
    }
  }

  ensureUrlKeepHtml(url: string) {
    this.checkStringEmptyError(url);
    if (url.endsWith(".html")) {
      return url;
    }
    return `${url}.html`;
  }

  ensureUrlStartSlash = (url: string) => {
    this.checkStringEmptyError(url);
    if (url.startsWith("/")) return url;
    return `/${url}`;
  };

  isStartWithPrefix(url: string, prefix: string): boolean {
    this.checkStringEmptyError(url);
    this.checkStringEmptyError(prefix);
    return url.startsWith(prefix);
  }

  isIndexHtml(url: string): boolean {
    return ![
      this.isStartWithPrefix(url, "/compliance"),
      this.isStartWithPrefix(url, "/twitter"),
    ].some(Boolean);
  }

  buildNewUrl(original_url: string): string {
    const url = new URL(original_url);
    let version = "";

    // disable cache
    IS_DEV && url.searchParams.append("timestamp", Date.now().toString());

    if (
      // 本地不做代理转发
      !IS_DEV &&
      url.searchParams.has("version")
    ) {
      version = url.searchParams.get("version") || "";
      url.searchParams.delete("version");

      if (!["default"].includes(version)) {
        const html = this.ensureUrlStartSlash(
          this.isIndexHtml(url.pathname) ? "/index.html" : this.ensureUrlKeepHtml(url.pathname),
        );
        const new_path = `version/${version}${html}`;

        console.log("[MultipleHTMLVersionStrage] new path: ", new_path);
        url.pathname = new_path;
      }
    }

    const ret = url.toString();
    return ret;
  }
}

registerRoute(({ request }) => {
  return request.mode === "navigate";
}, new MultipleHTMLVersionStrage());

if (IS_CACHE_ASSETS_ENABLE) {
  // registerRoute(
  //   ({ request }) => {
  //     return request.destination === "image";
  //   },
  //   new CacheFirst({
  //     cacheName: STORAGE_CS_IMAGE,
  //     plugins: [
  //       new ExpirationPlugin({
  //         maxAgeSeconds: 24 * 60 * 60,
  //         maxEntries: 20,
  //       }),
  //       new CacheableResponsePlugin({
  //         statuses: [200],
  //       }),
  //     ],
  //   }),
  // );

  // TODO: 缓存 js 的时候 max entries 似乎被忽略了
  // registerRoute(
  //   ({ request }) => {
  //     return request.destination === "script";
  //   },
  //   new StaleWhileRevalidate({
  //     cacheName: STORAGE_CS_JS,
  //     plugins: [
  //       new ExpirationPlugin({
  //         maxAgeSeconds: 24 * 60 * 60,
  //         maxEntries: 5,
  //       }),
  //       new CacheableResponsePlugin({
  //         statuses: [200],
  //       }),
  //     ],
  //   }),
  // );

  registerRoute(
    ({ request }) => {
      return request.destination === "style";
    },
    new StaleWhileRevalidate({
      cacheName: STORAGE_CS_CSS,
      plugins: [
        new ExpirationPlugin({
          maxAgeSeconds: 24 * 60 * 60,
          maxEntries: 20,
        }),
        new CacheableResponsePlugin({
          statuses: [200],
        }),
      ],
    }),
  );

  registerRoute(
    ({ request }) => {
      return request.destination === "font";
    },
    new StaleWhileRevalidate({
      cacheName: STORAGE_CS_FONT,
      plugins: [
        new ExpirationPlugin({
          maxAgeSeconds: 24 * 60 * 60,
          maxEntries: 20,
        }),
        new CacheableResponsePlugin({
          statuses: [200],
        }),
      ],
    }),
  );
}

self.addEventListener("message", (event) => {
  if (event.data && event.data.type === "SKIP_WAITING") {
    self.skipWaiting();
    // self.clients.claim();
  }
});
