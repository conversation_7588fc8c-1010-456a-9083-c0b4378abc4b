export default {
  purchase_limit_day: "1日の購入制限",
  purchase_limit_week: "週ごとの購入制限",
  purchase_limit_month: "月ごとの購入制限",
  redeem_deatil: "交換の詳細",
  records: "履歴",
  server: "サーバー",
  choose_server: "サーバーを選択してください",
  character: "キャラクター",
  choose_character: "キャラクターを選択してください",
  choose_character_tips: "キャラクターを選択してください",
  choose_region: "地域選択",
  all: "すべて",
  gain: "獲得した",
  redeemed: "交換した",
  redeem: "交換",
  overdue: "期限切れ",
  redeemed_x: "{0}を交換しました",
  failed_to_redeem_tips: "{0}の交換に失敗しました。コインを返却しています",
  expired: "期限切れ",
  gift: "贈り物",
  participate_activity: "活動に参加する",
  lucky_draw_reward: "累計ガチャ報酬",
  out_post: "前哨基地",
  nikke_art: "NIKKEアート",
  creator_hub: "Creator Hub",
  event: "イベント",
  follow: "フォロー",
  is_following: "フォロー済み",
  please_check_in_tomorrow: "サインイン済みです",
  check_in_tomorrow: "明日もサインインして、{0}コインをGETしよう！",
  play_games_tips: "ゲームをプレイして毎日{0}コインを獲得しましょう",
  task_tips: "クエストについて",
  play_game_tomorrow: "明日も『LEVEL INFINITE PASS』アカウントでログインして{0}コインをGETしよう！",
  please_play_game_tomorrow:
    "明日も『LEVEL INFINITE PASS』アカウントでログインしてコインをGETしよう！",
  check_in_fail: "サインインできませんでした",
  check_in_suc: "サインインできました",
  game_login: "ゲームプレイ",
  confirm: "決定",
  check_in: "サインイン",
  daily_quests: "デイリークエスト",
  available: "利用可能",
  total: "消費コイン",
  points_no_enouch: "コインが不足しています",
  back: "戻る",
  login: "ログイン",
  logout: "ログアウト",
  privacy: "プライバシーポリシー",
  cookies: "Cookieポリシー",
  contact_us: "お問い合わせ",
  privacyLink: "https://nikke-jp.com/privacypolicy/",
  cookiesLink: "https://nikke-jp.com/cookiepolicy/",
  rareness: "レア度",
  team: "部隊",
  attr_code: "コード",
  element: "コード",
  weapon: "武器",
  position: "クラス",
  class: "クラス",
  coporation: "企業",
  coperation: "企業",
  brust_stage: "バースト",
  weapontype: "武器",
  login_button: "ログイン",
  nav_nikke_list: "ニケ図鑑",
  nav_coming: "乞うご期待！",
  nav_gatcha: "募集記録",
  nav_main: "ホームへ",
  nav_scene_list: "ストーリー図鑑",
  switch_role: "アカウント切り替え",
  select_server_title: "サーバー選択",
  select_server_desc: "ゲームをプレイするサーバーを選択してください",
  select_server_no_role: "該当サーバーのアカウントが見つかりませんでした",
  select_server_loading: "検索中",
  select_server_nextstep: "次へ",
  select_role_title: "アカウント選択",
  select_role_desc: "アカウント名をご確認ください",
  select_role_confirm: "確認",
  select_role_reselect_server: "サーバーを選び直す",
  main_firstbind_tip1: "キャラクターとの初めての連携で、募集チケット×1が獲得できます",
  main_firstbind_tip2: "-",
  main_basic_info: "基本情報",
  main_remain_defense: "残り迎撃回数",
  main_rookie_battle: "ルーキーアリーナ",
  main_everyday_freetime: "1日の無料回数",
  main_special_battle: "スペシャルアリーナ",
  main_stage: "キャンペーン",
  main_tower: "タワー",
  main_layer: "階",
  main_power_nikke: "最も戦闘力が高いニケ",
  main_power_total: "合計戦闘力",
  main_nikke_num: "作戦人員",
  main_avatar_frame: "アイコンフレームの数",
  main_costume: "コスチューム",
  main_last_login: "最終ログイン日",
  main_outpost: "前哨基地情報",
  main_material: "保有アイテム",
  main_collapse: "閉じる",
  main_expand: "開く",
  elysion: "エリシオン",
  attacker: "火力型",
  tetra: "テトラ",
  defender: "防御型",
  pilgrim: "ピルグリム",
  supporter: "支援型",
  missilis: "ミシリス",
  abnormal: "アブノーマル",
  elysion_level: "エリシオン",
  attacker_level: "火力型",
  tetra_level: "テトラ",
  defender_level: "防御型",
  pilgrim_level: "ピルグリム",
  supporter_level: "支援型",
  missilis_level: "ミシリス",
  missills_level: "ミシリス",
  sychro_level: "シンクロレベル",
  abnormal_level: "アブノーマル",
  recyle_level: "リサイクルルームレベル",
  character_type_fire: "灼熱",
  character_type_electronic: "電撃",
  character_type_water: "水冷",
  character_type_iron: "鉄甲",
  character_type_wind: "風圧",
  ar: "AR",
  mg: "MG",
  smg: "SMG",
  sg: "SG",
  sr: "SR",
  rl: "RL",
  filter: "フィルター",
  close: "閉じる",
  sort: "並び替え",
  nikke_list_tab_all: "ニケ図鑑",
  nikke_list_tab_player: "私のニケ",
  nikke_power: "戦闘力",
  nikke_level: "レベル",
  nikke_break: "限界突破",
  nikke_attract_level: "好感度",
  nikke_rarity: "レア度",
  scene_tab_main: "メインストーリー",
  scene_tab_event: "突発ストーリー",
  scene_tab_archive: "アーカイブ",
  scene_unlock_tip: "未解放のストーリーです",
  cv_key_en: "英語",
  cv_key_ko: "韓国語",
  cv_key_ja: "日本語",
  use_material_upgrade_nikke: "素材を使用してニケの強化とレベルアップが可能",
  fight_attr: "戦闘力",
  hp_attr: "HP",
  atk_attr: "攻撃力",
  def_attr: "防御力",
  upgrade_material: "レベルアップ素材",
  upgrade_core_material: "コア強化素材",
  equipment: "装備",
  no_effects: "効果なし",
  skill: "スキル",
  attract_scene: "好感度エピソード",
  attractive_level_unlock: "好感度 {level} で解放",
  dialogue: "キャラの台詞",
  game_name: "勝利の女神：NIKKE",
  game_type: "ガンガールRPG",
  game_os: "iOS/Android",
  equip_effect: "装備改造効果",
  equip_stat: "-",
  no_effect: "効果なし",
  no_equip: "装備がありません",
  max_ammo: "最大装弾数",
  reload_time: "リロード時間",
  op_type: "操作タイプ",
  buff_detail: "追加ステータス",
  debuff: "デバフ",
  game_year: "Release：2022",
  dialogue_hint: "{chapter_name}のボイス図鑑です",
  chapter: "Chapter.{num}",
  default: "デフォルト",
  enter_battle_hint: "効果の数値は戦闘突入時に適用されます",
  filter_name_placeholder: "ニケの名前検索",
  clear_btn: "クリア",
  archive_tip: "ストーリーイベントを振り返ることができます",
  server_jp: "日本",
  server_na: "北米",
  server_global: "グローバル",
  server_kr: "韓国",
  server_sea: "東南アジア",
  date_attemp_time: "本日の参加回数",
  no_data: "データなし",
  data_sync_tip:
    "ShiftyPadはテスト期間中のため、以下の制限がございます：\n1) ゲームバージョン更新後、新リソースの同期に0.5～1日くらいかかります。\n2) データ変化の同期に1時間くらいかかります。\n3) ニケの戦闘力がゲームとわずかに異なる場合がございます。\n4) デフォルトでは、他のユーザーにあなたの「保有アイテム」と「私のニケ」を表示しません。右上の盾型アイコンをクリックして表示・非表示の設定が変更できます。\nまだ体験が良くないところがございますが、私どもは引き続き最適化を努力いたします。ご理解のほどよろしくお願いいたします！",
  notice: "注意",
  networkError: "ネットワークエラー",
  choice: "選択",
  help_tip:
    "こちらはSHIFTYPADです。SHIFTYPADのアークサポートシステムで必要な情報を検索できます。ただし、個人情報を他人に教えないでくださいね。指揮官のお役に立ちますように。",
  license_agreement: "License Agreement",
  input_type_up: "チャージ型",
  input_type_down: "通常型",
  ar_desc: "どんな状況にも対応できるようにバランスが取れている",
  smg_desc: "射程距離は比較的短いが、優れた連射力を持つ",
  sg_desc: "複数の弾丸が散乱するように発射され、近くの敵に強い威力を発揮する",
  sr_desc: "射程距離が長く精密な射撃ができるため、敵の特定部位を狙うことができる",
  rl_desc: "広い範囲を攻撃するため、密集している敵を相手にする時に効果的",
  mg_desc: "弾数が多く連射力が高いため、押し寄せる敵を相手にする時に効果的",
  game_gold: "クレジット",
  character_exp: "バトルデータセット",
  character_exp_2: "コアダスト",
  game_outpost_lobby: "ロビー",
  game_outpost_forepost: "前哨基地",
  game_outpost_grow: "獲得および成長",
  game_outpost_wild: "フィールド",
  game_outpost_battle: "戦闘",
  html_title: "SHIFTYPADへようこそ",
  order_pending: "保留中",
  order_error: "エラー",
  order_complete: "達成済み",
  order_closed: "完了済み",
  order_not_begin: "コイン不足、または在庫不足で注文ができませんでした",
  deduct_points_err: "画面を更新するか、カスタマーサポートまでご連絡ください",
  send_commodity_err: "画面を更新するか、カスタマーサポートまでご連絡ください",
  gift_package_distribution_completed:
    "ゲーム内のメールボックスからギフトパックをお受け取りください",
  deduct_points_failed_has_rollback: "コインの返却が完了しました",
  send_commodity_failed_has_rollback: "コインの返却が完了しました",
  rollback_points_error: "カスタマーサポートまでご連絡ください",
  send_zero_price_commodity_err: "無料アイテム配布エラー",
  role_not_found: "キャラクターが見つかりません",
  purchase_limit: "期間限定販売",
  purchase_user_limit: "個人の受け取り上限に達しました",
  share: "シェア",
  success_redeemed_tips: "ご注文状況をご確認の上、ゲーム内のメールボックスをご確認ください",
  order_status: "ステータス",
  trading_hours: "日時",
  consumption_points: "消費コイン",
  coin: "コイン",
  trade_name: "アイテム",
  order_no: "注文番号",
  redeemed_check_mailbox: "ご注文状況をご確認の上、ゲーム内のメールボックスをご確認ください",
  failed_redeemed: "交換失敗",
  success_redeemed: "交換完了",
  redeem_rewards: "報酬交換",
  loading_role: "ローディング",
  reward: "報酬",
  following: "フォロー",
  follower: "フォロワー",
  his_following: "フォロー",
  his_follower: "フォロワー",
  edit_nickname_tips: "本名を使用しないでください",
  game_tag: "ゲームタグ",
  graphics: "画像",
  graphics_and_text: "投稿",
  video: "動画",
  publish: "投稿する",
  title: "タイトル",
  show_me_posts_desc: "ホームページで投稿を表示する",
  show_me_collection_desc: "ホームページでお気に入りの投稿を表示する",
  show_me_fans_desc: "ホームページでフォロー数を表示する",
  show_me_game_card_desc: "ホームページでフォロワー数を表示する",
  receive_tweet_emial_desc: "ニュースに関するメールを受信する",
  msg_comment_notify_desc: "コメント/リプライ",
  msg_like_notify_desc: "いいね",
  msg_follow_notify_desc: "新規フォロワー",
  msg_system_notify_desc: "システム通知",
  msg_activity_notify_desc: "イベント通知",
  max_image_limit_size_tips: "最大{0}までアップロード可能",
  file_upload_failed: "ファイルのアップロードに失敗しました",
  manage: "管理",
  home_links: "リンク",
  links_manage: "サードパーティーのリンク管理",
  personal_setting: "個人設定",
  ads_setting: "プロモーション設定",
  more: "もっと見る",
  please_paste_the_link_here: "URLを入力してください",
  sure: "もちろん",
  loading: "ローディング",
  video_parse_failed:
    "リンクが解析できませんでした。正確なYouTube、TikTokのリンクを入力してください",
  bind_game_character_tips: "ゲームのキャラクターと連携します",
  bind_lip_tips:
    "ゲームキャラクターと『LEVEL INFINITE PASS』アカウントを連携して、コイン交換イベントに参加しましょう！",
  bind_lip_title: "アカウント連携",
  my_posts: "投稿",
  my_comments: "コメント",
  my_favorites: "お気に入り",
  his_posts: "投稿",
  his_comments: "コメント",
  his_favorites: "お気に入り",
  delete_post: "投稿の削除",
  delete_comment: "コメントを削除する",
  post_successfully: "投稿しました",
  nickname: "ニックネーム",
  signature: "ステータスメッセージ",
  abandon_the_post_title: "ポストを放棄しますか？",
  abandon_the_post_content: "今すぐ放棄すれば、ポストは失われます",
  abandon_the_post_confirm_text: "編集を続ける",
  abandon: "削除する",
  alert: "注意",
  content_will_no_be_saved: "現在の内容は保存されません",
  keep_the_current_content: "現在の内容を新しいセクションに保持しますか?",
  keep: "キープ",
  replace_video: "動画を置き換える",
  create_post: "投稿を作成する",
  support: "サポート",
  video_link: "動画URL",
  replace_the_video: "動画を置き換える",
  replace_the_video_content:
    "削除すると、すべての内容が削除され、動画URLの再入力が必要となります。",
  cancel: "キャンセル",
  insert_picture: "画像を挿入",
  picture: "画像",
  camera: "カメラ",
  editor_default_placeholder: "内容を入力してください",
  required: "必須",
  reach_comment_list_bottom: "すべてのコメントが表示されました",
  comment: "コメント",
  reply: "リプライ",
  send: "送信",
  notification_setting: "通知設定",
  related_to_me: "あなたへの通知",
  notifications: "通知",
  delete_successfully: "削除されました",
  are_you_sure_to_delete: "削除してもよろしいですか？",
  delete: "削除",
  select_reason_for_reporting: "理由を入力してください",
  unfollowed: "フォロー解除",
  are_you_sure_to_unfollow: "フォローを解除しますか？",
  keep_follow: "フォローし続ける",
  unfollow: "フォローを解除する",
  post: "投稿",
  logging_out: "ログアウト中",
  could_not_find_any_results: "申し訳ありませんが、結果は見つかりませんでした",
  link_required: "リンク（必須）",
  description_optional: "説明（任意）",
  min_before: "分前",
  hour_before: "時間前",
  posted: "投稿日",
  there_is_not_anything: "該当する内容がありません",
  come_to_grab: "1コメをゲットしよう",
  first_comment: "1コメ",
  poster_does_not_reply: "投稿者からリプライがありません",
  hot: "人気",
  latest: "最新",
  share_my_opinion: "コメントを投稿",
  please_enter: "入力してください",
  battle_hard: "キャンペーン（ハード）",
  battle_normal: "キャンペーン（ノーマル）",
  author: "投稿者",
  user_status: "個人のステータス",
  recommend: "おすすめ",
  first_time_binding_a_role_can_earn:
    "キャラクターとの初めての連携で、募集チケット×1が獲得できます",
  bind: "連携",
  not_used_recently: "最近使用されていない",
  insert_link: "リンクを挿入する",
  search_more_topics: "トピックを検索する",
  posts: "投稿",
  all_regions: "すべての地域",
  comment_successfully: "コメントが投稿されました",
  original_text: "原文",
  all_comments: "全てのコメント",
  reporting: "通報",
  editing: "編集",
  move_to: "に投稿を移動する",
  actions: "オプション",
  advertise: "広告",
  suspected_of_swiping_credit_card: "スパムの疑い",
  suspected_of_uncivilized_behavior: "マナー違反行為の疑い",
  suspected_of_spreading_false_information: "虚偽の情報を散布した疑い",
  suspected_trading_or_gaming_services: "アカウントやゲーム内資産売買の疑い",
  others: "その他",
  module: "モジュール",
  area: "エリア",
  move_contents_to: "に投稿を移動する",
  post_detail: "投稿の詳細",
  report_successfully: "通報済み",
  error_video_platform_parse_tips:
    "リンクが解析できませんでした。正確なYouTube、TikTokのリンクを入力してください",
  exceed_max_topic_tips: "トピックを最大{0}個選択してください",
  move_successfully: "移動しました",
  clear_all: "すべてクリア",
  invalid_link: "無効なリンク",
  enter_valid_url_tips: "有効なURLを入力してください",
  enter_channel_url_tips: "対応チャネルのリンクを入力してください",
  select_publishing_section: "セクションを選択",
  select_publishing_area: "地域を選択",
  select_publishing_lang: "言語を選択",
  show_my_posts: "ホームページで投稿を表示する",
  show_my_collection: "ホームページでお気に入りの投稿を表示する",
  show_my_follow: "ホームページでフォロー数を表示する",
  show_my_fans: "ホームページでフォロワー数を表示する",
  receive_tweet_email: "情報メールを受け取る",
  account_setting: "アカウント設定",
  account_management: "アカウント管理",
  private_setting: "プライバシー設定",
  language_setting: "言語設定",
  about: "{0} について",
  terms_of_service: "{0} 利用規約",
  privacy_policy: "{0} プライバシーポリシー",
  li_pass_terms_of_service: "LEVEL INFINITE PASS 利用規約",
  li_pass_privacy_policy: "LEVEL INFINITE PASS 個人情報取り扱い同意書",
  nikke_channel: "NIKKE公式チャンネル",
  li_pass_channel: "LEVEL INFINITE PASS公式チャンネル",
  nikke_official: "NIKKE公式",
  li_pass_official: "LEVEL INFINITE PASS公式",
  nikke_official_group: "NIKKE公式グループチャット",
  li_pass_official_group: "LEVEL INFINITE PASS公式グループチャット",
  audit_username: "ユーザー名が審査中です",
  audit_remark: "ステータスメッセージ審査中",
  basic_info: "基本情報",
  account_links: "アカウント連携",
  my_account: "マイアカウント",
  country: "国/地域",
  compose_comment_img_limit_tips: "1つの画像のみ挿入できます",
  reward_center: "報酬センター",
  sign_in_failed: "サインインできませんでした",
  sign_in_failed_tips: "今日はすでにサインインしています。",
  edit_my_account: "プロフィール編集",
  edit_nickname: "ニックネームを入力",
  save: "保存",
  edit_signature: "ステータスメッセージを入力",
  login_banner_tips: "初回登録すると報酬を受け取ることができます",
  battle: "キャンペーン",
  normal_tag: "NORMAL",
  hard_tag: "HARD",
  select_role_to_get_reward:
    "ゲームキャラクターを選択してLEVEL INFINITE PASSバンドルギフトを入手してください！",
  api_code_1001009: "クエストはすでに完了しています",
  api_code_1100010: "交換額の上限を超えています",
  api_code_1100006: "現在交換できません",
  api_code_1100007: "交換は終了しました",
  api_code_1102001: "コインが不足しています",
  api_code_err_network: "ネットワークエラー",
  api_code_1200002: "ニュースコンテンツの検証に失敗しました",
  api_code_1200003: "レビューの作成に失敗しました",
  api_code_1200004: "レビューの更新に失敗しました",
  api_code_1200005: "HTTPリクエストが空オブジェクトを返しました",
  api_code_1200006: "HTTPリクエストがエラーをリターンしました",
  api_code_1200007: "HTTPがエラーステータスコードをリターンしました",
  api_code_1200008: "x-common-paramsが空です",
  api_code_1200009: "x-common-paramsの逆シリアル化に失敗しました",
  api_code_1200010: "リッチテキスト解析に失敗しました",
  api_code_1200011: "タイトルが25文字を越えているため、投稿に失敗しました",
  api_code_1200012: "本文が5,000文字を越えているため、投稿に失敗しました",
  api_code_1200013: "本文が500文字を越えているため、投稿に失敗しました",
  api_code_1200014: "画像サイズが制限を超えました",
  api_code_1200015: "リッチテキストには違法な画像が含まれています",
  api_code_1200016: "ブラックリスト、ホワイトリストの取得に失敗しました",
  api_code_1200017: "公開に失敗しました、ブラックリストに登録されました",
  api_code_1200018: "投稿の頻度が高すぎます。しばらくお待ちください",
  api_code_1200019: "投稿の頻度が高すぎます。しばらくお待ちください",
  api_code_1200020: "投稿の頻度が高すぎます。しばらくお待ちください",
  api_code_1200021: "リッチテキストのメインコンテンツ読み取りに失敗しました",
  api_code_1200022: "投稿は存在しません",
  api_code_1200023: "投稿に失敗しました",
  api_code_1200024: "投稿詳細の取得に失敗しました",
  api_code_1200025: "投稿の削除に失敗しました",
  api_code_1200026: "ユーザーデータ取得に失敗しました",
  api_code_1200027: "権限がないため、投稿の削除に失敗しました",
  api_code_1200037: "cmsリクエスト後にESでの投稿の更新に失敗しました",
  api_code_1200038: "公式アカウントが見当たりません",
  api_code_1200039: "cmsは情報の下のメッセージの削除に失敗しました",
  api_code_1200051: "リリース時間は現在時刻より15分以上後である必要があります",
  api_code_1200052: "権限がありません",
  api_code_1200053: "投稿の閲覧権限を変更できませんでした",
  api_code_1200054: "投稿の審査情報を取得できませんでした",
  api_code_1200055: "投稿の内容を取得できませんでした",
  api_code_1200056: "ホームページのダイナミックリストを取得する際のエラー",
  api_code_1200057: "トピックの取得に失敗しました",
  api_code_1200058: "トピックのお気に入り作成に失敗しました",
  api_code_1200059: "トピックのお気に入り削除に失敗しました",
  api_code_1200060: "ユーザー文章のお気に入り登録状態の取得に失敗しました",
  api_code_1200061: "情報が未承認のため、コレクションを許可したくない",
  api_code_1200062: "ニュースのお気に入り登録に失敗しました",
  api_code_1200063: "ニュースのお気に入り削除に失敗しました",
  api_code_1200064: "お気に入り登録、削除操作が頻繁過ぎます",
  api_code_1200065: "お気に入りフォルダの投稿リストを獲得できませんでした",
  api_code_1200066: "コメント状態の取得に失敗しました",
  api_code_1200067: "ニュースのいいね状態の取得に失敗しました",
  api_code_1200068: "いいねに失敗しました",
  api_code_1200069: "いいねの取消に失敗しました",
  api_code_1200070: "いいね、取消操作が頻繁過ぎます",
  api_code_1200071: "トピックの作成に失敗しました",
  api_code_1200072: "トピックレビューフローの作成に失敗しました",
  api_code_1200073: "フォローリストの取得に失敗しました",
  api_code_1200074: "ユーザー情報の取得に失敗しました",
  api_code_1200075: "フォローリストの取得に失敗しました",
  api_code_1200076: "フォローに失敗しました",
  api_code_1200077: "フォローの解除に失敗しました",
  api_code_1200078: "フォローまたは解除操作が頻繁過ぎます",
  api_code_1200079: "称号の取得に失敗しました",
  api_code_1200080: "本日DMを送信する回数が上限に達しています",
  api_code_1200081: "ユーザーの初回ログインポイントメッセージの取得に失敗しました",
  api_code_1200082: "メッセージの送信に失敗しました",
  api_code_1200083: "投稿リストの取得に失敗しました",
  api_code_1200084: "コメントリストの取得に失敗しました",
  api_code_1200085: "間違った報告タイプ",
  api_code_1200086: "自分を通報できません",
  api_code_1200087: "通報データの作成に失敗しました",
  api_code_1200088: "1分間のコメント上限に達しました",
  api_code_1200089: "1時間のコメント上限に達しました",
  api_code_1200090: "1日のコメント上限に達しました",
  api_code_1200091: "コメントの送信に失敗しました",
  api_code_1200092: "コメントの送信に失敗しました。この投稿は現在審査中です",
  api_code_1200093: "最大コメント数を超えました",
  api_code_1200094: "リプライに失敗しました",
  api_code_1200095: "リプライに失敗しました。この投稿は現在審査中です",
  api_code_1200096: "返信を作成する際に情報が審査されていません",
  api_code_1200097: "このコメントは現在審査中のため、いいねできません",
  api_code_1200098: "コメントのいいね情報の取得に失敗しました",
  api_code_1200099: "コメントへのいいねに失敗しました",
  api_code_1200100: "コメントへのいいねの取り消しに失敗しました",
  api_code_1200101: "コメントにいいねできる回数が上限に達しました",
  api_code_1200102: "コメントがありません",
  api_code_1200103: "パラメーターエラー",
  api_code_1200104: "ホワイトリストの取得に失敗しました",
  api_code_1200105: "ブラックリスト、ホワイトリストの審査リストの取得に失敗しました",
  api_code_1200106: "ユーザー情報の取得に失敗しました",
  api_code_1200107: "ユーザーの初回登録状態の読み取りに失敗しました",
  api_code_1200108: "プライバシーポリシーの更新に失敗しました",
  api_code_1200109: "称号の取得に失敗しました",
  api_code_1200110: "ランダムなニックネームが最大制限に達しました",
  api_code_1200111: "ニックネームの作成に失敗しました",
  api_code_1200112: "アバターリストの取得に失敗しました",
  api_code_1200113: "新規登録に失敗しました",
  api_code_1200114: "ユーザー情報の更新に失敗しました",
  api_code_1200115: "ニックネームの更新に失敗しました",
  api_code_1200116: "ニックネーム審査に対応するOpenIDがありません",
  api_code_1200117: "新しいニックネームは審査中です",
  api_code_1200118: "ステータスに対応するOpenIDがありません",
  api_code_1200119: "新しいステータスは審査中です",
  api_code_1200120: "既に存在するニックネームです",
  api_code_1200121: "ニックネームが長すぎます",
  api_code_1200122: "不適切なニックネームです",
  api_code_1200123: "未成年ユーザーです",
  api_code_1200124: "審査データリストの取得に失敗しました",
  api_code_1200125: "プロフィール情報の更新に失敗しました",
  api_code_1200126: "ステータスの更新に失敗しました",
  api_code_1200127: "URLエラー",
  api_code_1200128: "アバターURL解析エラー",
  api_code_1200129: "アバターにリンクされたゲームのリストを取得する際にエラーが発生しました",
  api_code_1200130: "コメント内容の取得に失敗しました",
  api_code_1200131: "リプライリストの取得に失敗しました",
  api_code_1200132: "内部メッセージのJSONの逆シリアル化に失敗しました",
  api_code_1200133: "内部メッセージをJSONにシリアル化できませんでした",
  api_code_1200134: "メッセージをキューに書き込むのに失敗しました",
  api_code_1200135: "内部メッセージのキャッシュ取得に失敗しました",
  api_code_1200136: "内部メッセージ情報の取得に失敗しました",
  api_code_1200137: "ユーザーの内部メッセージの作成に失敗しました",
  api_code_1200138: "内部メッセージの状態を変更できませんでした",
  api_code_1200139: "存在しないプッシュタイプです",
  api_code_1200140: "内部メッセージユーザー番号パッケージのインポート状態の変更に失敗しました",
  api_code_1200141: "テーブル名が存在しません",
  api_code_1200142: "全ユーザーを取得できません",
  api_code_1200143: "内部シグナルコードパッケージの読み取りに失敗しました",
  api_code_1200144: "ファイルのダウンロードに失敗しました",
  api_code_1200145: "サイト内メッセージkafkaの書き込みに失敗しました",
  api_code_1200146: "投稿のいいね数の更新に失敗しました",
  api_code_1200147: "COS構成情報の取得中に例外が発生しました",
  api_code_1200148: "メッセージリストの取得に失敗しました",
  api_code_1200149: "ユーザープライバシースイッチの逆シリアル化スイッチに失敗しました",
  api_code_1200150: "ユーザープライバシーポリシースイッチの取得に失敗しました",
  api_code_1200151: "ユーザープライバシーポリシースイッチタイプが無効です",
  api_code_1200155: "ビデオリンクの解析エラー",
  api_code_1200156: "ビデオアドレスの取得に失敗しました",
  api_code_1200157: "ビデオアドレスの解析に失敗しました",
  api_code_1200158: "Kong構成の取得に失敗しました",
  api_code_1200159: "スタンプリスト読み取りエラー",
  api_code_1200160: "絵文字関連リストの取得に失敗しました",
  api_code_1200161: "個人のステータスの設定に失敗しました",
  api_code_1200162: "再フォローに失敗しました",
  api_code_1200163: "ニュースのお気に入り再登録に失敗しました",
  api_code_1200164: "投稿のいいね情報の解析に失敗しました",
  api_code_1200165: "投稿のいいね情報取得に失敗しました",
  api_code_1200166: "発言禁止ユーザーの情報の更新に失敗しました",
  api_code_1200167: "発言禁止ユーザーの情報の削除に失敗しました",
  api_code_1200168: "管理者情報の更新に失敗しました",
  api_code_1200169: "管理者情報の削除に失敗しました",
  api_code_1200170: "認証済みユーザーの情報の更新に失敗しました",
  api_code_1200171: "認証済みユーザーの情報の削除に失敗しました",
  api_code_1200172: "既に通報しています",
  api_code_1200173: "投稿内容の更新に失敗しました",
  api_code_1200174: "レポート内容の更新に失敗しました",
  api_code_1200175: "ユーザーの審査データ記録の更新に失敗しました",
  api_code_1200176: "投稿の移動に失敗しました",
  api_code_1200177: "その言語はサポートされていません",
  api_code_1200178: "NIKKEゲーム情報の検索に失敗しました",
  api_code_1200179: "プライバシー設定により、このプレイヤーゲームの状況を確認できません",
  api_code_1200180: "NIKKEゲームタグの設定に失敗しました",
  api_code_1200181: "画像付き投稿を選択したため、先に画像をアップしてください",
  api_code_1200182: "投稿のいいね数の更新に失敗しました",
  api_code_1200183: "コメント審査が完了しました",
  api_code_1200184: "投稿審査が完了しました",
  api_code_1200185: "セクションの取得に失敗しました",
  api_code_1200186: "イベントをJSONに変換できませんでした",
  api_code_1200187: "ページネーションカーソルが無効です",
  api_code_1200188: "ページネーションカーソルが無効です",
  api_code_1200189: "内部メッセージのすべての情報のマッピングに異常があります",
  api_code_1200190: "イベントリスト取得エラー",
  api_code_1200191: "多言語イベントリスト取得エラー",
  api_code_1200192: "アクティビティをリストに挿入する際のエラー",
  api_code_1200193: "AIGC翻訳機能サービス呼び出しエラー",
  api_code_1200194: "AIGC翻訳機能サービスのレスポンス解析エラー",
  api_code_1200195: "翻訳キャッシュデータ読み取りエラー",
  api_code_1200196: "原文と訳文の数が合っていません",
  api_code_1200197: "セクションの多言語マッピングの取得に失敗しました",
  api_code_1200198: "審査されていない投稿をシェアできません",
  api_code_210002: "このアカウントはテストに参加していません",
  api_code_1200199: "アバターペンダントの設定に失敗しました",
  api_code_1200200: "ユーザーアバターペンダントリストの解析に失敗しました",
  api_code_1200201: "ユーザーアバターバッジリストの取得に失敗しました",
  api_code_1200202: "アバターペンダントの設定に失敗しました",
  api_code_1200203: "プロフィールペンダントが取得されていません",
  api_code_1200204: "アバターペンダントの取得に失敗しました",
  show_my_comment: "ホームページで私のコメントを表示する",
  edit_link: "リンクを編集する",
  edit_avatar: "アバターを変える",
  modify_username: "ニックネームを入力",
  modify_successfully: "変更が成功しました",
  additional_information: "追加情報",
  have_agree_agreement: "以下の規約を読んだうえで、同意します",
  sign_successfully: "サイン完了",
  load_content_error: "コンテンツの読み込みエラー",
  comments: "コメント",
  new_follower: "新規フォロワー",
  received_like: "いいね数",
  loaded_comments: "すべてのコメントが表示されました",
  loaded_replies: "すべての返信が表示されました。",
  violation_tips: "コミュニティガイドラインに違反したため禁止されました。",
  history: "検索履歴",
  view_faqs: "<a class='{0}'>FAQ</a>で詳細を確認",
  faq: "FAQ",
  user_already_set_private: "プレイヤーはこのページを非公開に設定しました。",
  please_choose_region: "地域選択",
  sold_out: "完売",
  nikke_area_29080: "日本/韓国/北米/東南アジア/グローバル",
  nikke_area_29157: "香港/マカオ/台湾",
  nikke_end_user_license_agreement: "NIKKEエンドユーザーライセンス契約",
  nikke_privacy_policy: "NIKKEプライバシーポリシー",
  share_to: "シェアする",
  add_to_home_screen: "ホーム画面に追加",
  add_to_home_screen_to_find_us_easily:
    "デスクトップにショートカットを追加すると、次から見つけやすくなります",
  open_in_browser: "ブラウザで開く",
  just_tap_share: "「シェア」をタップするだけです",
  "then_add to_home_screen": "次に「ホーム画面に追加」",
  no_roles: "キャラクターが見つかりません",
  this_project_is_under_construction: "開発中です。お楽しみに",
  please_rotate_your_device_for_better_display: "表示を改善するためにデバイスを回転させてください",
  query_data_ad: "便利なクエリ [Nikkeゲームデータ]",
  compaign_list: "ストーリー図鑑",
  copy_failed: "コピーに失敗しました",
  share_channel: "{channel} にシェアする",
  share_failed: "シェア失敗しました",
  bind_lip_first: "LI Passアカウントを連携してください",
  receive_gitf_success: "ギフトが送信されました",
  bound_get_gift: "連携してギフトをゲット",
  channel_bounded: "すでに連携されています",
  link_channel: "連携",
  view_all: "すべて表示",
  bind_role_bonus_hint: "キャラクターとの初めての連携で、{0}が獲得できます",
  please_login: "ログインしてください",
  view_game_data: "ゲームデータを確認します",
  copy_link: "リンクのコピー",
  system_share: "システム",
  copy_link_success: "コピーしました",
  bind_role_success: "キャラクター連携完了",
  intl_sdk_fail: "ネットワークエラー。ログイン機能の読み込みに失敗しました",
  action_frequency_tips: "操作が多すぎます。しばらくしてから再試行してください。",
  view_original_article: "原文を見る",
  browser_not_support:
    "現在のプラットフォームはこの操作をサポートしていません。別のプラットフォームをお試しください",
  openid_is_bound:
    "この LEVEL INFINITE PASS アカウントは既にゲーム キャラクターに連携されています。別のアカウントでもう一度お試しください。",
  shiftyspad_root_share:
    "自分のゲームデータをいつでもチェックできる、NIKKE専用のゲームツール #Blabla Link",
  shiftys_spad: "SHIFTYPAD",
  edit_user_link_tips:
    "外部リンクを追加します。個人プロフィールに表示され、他のユーザーにも表示されます。",
  communityguidelines: "コミュニティガイドライン",
  opensource_statement: "オープンソースソフトウェア声明",
  notification_message_type_2: "が投稿にコメントしました",
  notification_message_type_3: "が投稿のコメントにリプライしました",
  notification_message_type_5: "が投稿をいいねしました",
  notification_message_type_6: "にフォローされました",
  notification_message_type_7: "あなたの投稿「{0}」は「{1}」のため、管理者によって削除されました",
  notification_message_type_8:
    "あなたのコメント「{0}」は「{1}」のため、管理者によって削除されました",
  notification_message_type_9: "が投稿のコメントにいいねしました",
  notification_message_type_10: "が投稿のコメントにいいねしました",
  notification_message_type_11:
    "「{1}」のため、あなたのニックネームの審査が通過しませんでした。速やかに修正してください。",
  notification_message_type_12:
    "「{1}」のため、あなたの署名審査は通過しませんでしたので、速やかに修正してください",
  notification_message_type_13:
    "「{1}」のため、あなたが投稿した内容は審査に通過しませんでした。修正して再投稿してください。",
  notification_message_type_14:
    "あなたのコメント「{0}」は「{1}」のため、管理者によって削除されました",
  notification_message_type_18:
    "「{1}」のため、あなたのニックネームの審査が通過しませんでした。適時修正してください。",
  notification_message_type_19:
    "「{1}」のため、あなたの署名審査は通過しませんでしたので、速やかに修正してください",
  notification_message_type_20: "{0}",
  notification_message_type_21: "通報いただいたコメント/投稿“{0}”は受理され、削除されました",
  notification_message_type_22:
    "「{1}」のため、あなたはシステムによって{0}日間ミュートされました。質問がある場合は、メールでお問い合わせください。フィードバックメール：{2}",
  content_deleted: "削除された内容です",
  content_under_review: "審査中の内容です",
  app_name: "Blabla Link",
  page_content_not_found: "内容がありません",
  translate: "翻訳する",
  view_original: "原文を確認",
  all_events: "全てのイベント",
  all_categories: "全てのカテゴリー",
  login_to_see_more: "ログインしてもっと見る",
  translating: "翻訳しています",
  add_new_language_version: "多言語バージョン追加",
  scheduled_release: "予約投稿",
  period_of_validity: "有効期限",
  avatar_frame_locked: "未解放",
  edit_avatarframe: "アバターアクセサリー",
  permanent_valid: "無期限",
  to_get: "獲得する",
  wear: "セットする",
  demount: "外す",
  edit_post: "投稿を編集",
  year: "年",
  month: "月",
  day: "日",
  hour: "時間",
  minute: "分",
  second: "秒",
  to_edit_avatar_frame: "アバターアクセサリーを編集する",
  file_type_not_support: "対応できないフォーマットです。 {0}のみ対応可能",
  unrelease: "未投稿",
  share_shiftyspad_text:
    "自分のゲームデータをいつでもチェックできる、NIKKE専用のゲームツール#BlablaLink",
  share_topic_text:
    "Blabla LinkにはNIKKEの面白い話題が盛りだくさん。さっそくトークに参加してみよう#{0}",
  share_points_text: "NIKKE公式のゲームコミュニティで、たくさんのゲーム特典をGETしよう#BlablaLink",
  share_others_text:
    "ゲームツール、ゲーム特典、最新情報の全てが詰まった、NIKKE公式のゲームコミュニティ#BlablaLink",
  system_notifications: "システム",
  redeem_successful: "交換完了",
  redeem_successful_pop_content:
    "ご注文状況をご確認の上、ゲーム内のメールボックスでお受け取りください",
  shiftyspad_private_setting: "SHIFTYPADプライバシー設定",
  shiftyspad_settings_tips: "SHIFTYPADで自分の{0}を表示する",
  add_to_screen_title: "{0}へクイックアクセス",
  add_to_screen_step_1: "ステップ1：トップページ{1}の{0}をタップ",
  add_to_screen_step_2: "ステップ2：{0}を選択し、確認ボタンを押して追加",
  add_to_screen_step_3: "ステップ3：{0}をタップして{1}を開く",
  add_to_screen_step_1_game: "ステップ1：トップページ{1}の{0}をタップして、{2}を選択",
  add_to_screen_step_1_game_andorid:
    "ステップ1：トップページ{1}の{0}をタップして、Goolgle Chromeを選択",
  add_to_screen_step_2_game: "ステップ2：{1}の{0}をタップして{2}を選択し、確認ボタンを押して追加",
  desktop_shortcut: "デスクトップのショートカット",
  cdk_redemption: "CDK交換",
  redemption_record: "交換履歴",
  success: "成功",
  failed: "失敗",
  main_daily_info: "デイリーニュース",
  none_disclosure_agreement: "{0}秘密保持契約",
  synthesis: "総合",
  topic: "トピック",
  user: "ユーザー",
  api_code_1302001: "ゲームがフィードバックしたエラーコードをマッチできません",
  api_code_1302002: "パラメーターエラー",
  api_code_1302003: "無効な交換コードです",
  api_code_1302004: "GameIDパラメータの取得に失敗しました",
  api_code_1302005: "OpenIDパラメータの取得に失敗しました",
  api_code_1302006: "RoleIDパラメータの取得に失敗しました",
  api_code_1302007: "システムリクエストにエラーが発生。もう一度お試しください",
  api_code_1302008: "交換権限がありません",
  api_code_1302009: "イベント開催時間外です",
  api_code_1302010: "このイベントは指定サーバーのユーザーのみ交換できます",
  api_code_1302011: "このイベントは指定国/地域のユーザーのみ交換できます",
  api_code_1302012:
    "このイベントは指定プラットフォーム（IOS、androidなど）のユーザーのみ交換できます",
  api_code_1302013: "MRMSパックを配布できませんでした",
  api_code_1302014: "このCDKコードは他のユーザーに使用されました",
  api_code_1302015: "存在しないCDKコードです",
  api_code_1302016: "既にこのCDKコードを使用しています",
  api_code_1302017: "CDKコード/ユーザーの交換回数が上限に達しています",
  api_code_1302018:
    "アクセスの頻度が高すぎます。現在このCDKコード使用しています。しばらく待ってから、もう一度お試しください",
  api_code_1302019: "ネットワークエラー。しばらく待ってから、もう一度お試しください",
  please_link_character: "キャラクターと連携してください",
  show_my_game_card: "ホームページでゲームカード（基本情報）を表示する",
  please_enter_cdk: "CDKコードを入力してください",
  cdk_too_long: "CDKコードが長すぎます（最大32文字）",
  share_cdk_text: "NIKKEのCDKコードの交換は、便利で使いやすいBlabla Linkへ",
  notification_message_type_22_reason_00: "その他の原因",
  notification_message_type_22_reason_01:
    "不必要な内容を投稿しています（NIKKEと関係がない、または重複している）",
  notification_message_type_22_reason_02:
    "コミュニティの雰囲気を悪化させる内容です（暴言、煽り、誹謗中傷など）",
  notification_message_type_22_reason_03:
    "違法または不適切な内容です（違法な内容、暴力的、性的表現など）",
  notification_message_type_22_reason_04:
    "ゲームの公平性を損なう内容です（バグの悪用、チート行為、改ざんなど）",
  notification_message_type_22_reason_05: "スタッフや運営、プレイヤーを装っています",
  notification_message_type_22_reason_06: "ニックネームまたは自己紹介が規則に違反しています",
  notification_message_type_22_reason_07:
    "ゲームパッケージから解凍したコンテンツまたは事実と異なるアップデート情報、虚偽の情報などを投稿しています",
  notification_message_type_22_reason_08:
    "アカウントの譲渡や売買、評価、代理プレーなど、不正な取引の情報を投稿しています",
  notification_message_type_22_reason_09: "宣伝や営業活動を行っています",
  notification_message_type_22_reason_10: "コミュニティーの創作や転載に関する規定に違反しています",
  jump_outside_title_tips: "まもなく外部のWebサイトに移動します",
  jump_outside_warning_tips:
    "まもなく外部のWebサイトに移動します。安全ではない可能性がありますので、ご注意ください",
  warning: "警告",
  account_not_bind_game_role_title: "カードの追加に失敗しました",
  account_not_bind_game_role_tips:
    "カードを追加する前に、ゲーム内のキャラクターをリンクしてください",
  account_not_bind_game_role_confirm_btn_text: "キャラクターと連携する",
  request: "申請する",
  api_code_1200235: "フレンド申請は自分に対してはできません",
  api_code_1302101: "キャラクターが見つかりません",
  api_code_1302102: "キャラクターはこのサーバーにはありません",
  api_code_1302103: "通信エラーです。インターフェースの呼び出しに失敗しました",
  api_code_1302104: "フレンド申請できませんでした。申請数が上限の30人に達しています",
  api_code_1302105:
    "フレンド申請できませんでした。相手が受け取った申請数が上限の30人に達しています",
  api_code_1302106: "フレンド申請できませんでした。相手のフレンド数が上限の30人に達しています",
  api_code_1302107: "フレンド申請をすでに送っています",
  api_code_1302108: "このユーザーは存在しません（アカウントが削除された可能性があります）",
  api_code_1302109: "フレンド登録に失敗しました",
  comment_decorations: "コメントフレーム",
  personalized: "カスタマイズ",
  content_delete_reason_1: "不必要な内容を投稿している（NIKKEと関係がない、または重複する内容）",
  content_delete_reason_2:
    "コミュニティの調和を乱す行為（汚い言葉、個人攻撃、煽り、根拠のない非難など）。",
  content_delete_reason_10:
    "ポルノコンテンツの投稿（性的行為の明示や暗示的な描写、胸部や局部の過度な強調や露出、過度に露出した服装など）。",
  content_delete_reason_3:
    "不適切なコンテンツの投稿（暴力、血みどろ、グロテスク、猟奇的、違法行為、自傷行為や自殺の示唆、その他の悪意あるコンテンツ）。",
  content_delete_reason_4:
    "ゲームの公平性を損なう内容を投稿している（バグの悪用、チート行為、改ざんなど）",
  content_delete_reason_5:
    "ゲームパッケージから解凍したコンテンツまたは事実と異なるアップデート情報、虚偽の情報などを投稿している",
  content_delete_reason_6:
    "アカウントの譲渡や売買、評価、代理プレーなど、不正な取引の情報を投稿している",
  content_delete_reason_7: "宣伝や営業活動を行っている",
  content_delete_reason_8: "コミュニティーの創作や転載に関する規定に違反している",
  content_delete_reason_9: "その他の規定違反",
  select_reason_for_delete: "削除する理由を選択してください",
  garbage_content: "不必要な内容を投稿している（NIKKEと関係がない、または重複する内容）",
  community_disruption:
    "コミュニティの調和を乱す行為（汚い言葉、個人攻撃、煽り、根拠のない非難など）。",
  illegal_content:
    "不適切なコンテンツの投稿（暴力、血みどろ、グロテスク、猟奇的、違法行為、自傷行為や自殺の示唆、その他の悪意あるコンテンツ）。",
  crack_and_modify_game_balance:
    "ゲームの公平性を損なう内容を投稿している（バグの悪用、チート行為、改ざんなど）",
  unpack_content_information_violation:
    "ゲームパッケージから解凍したコンテンツまたは事実と異なるアップデート情報、虚偽の情報などを投稿している",
  account_transaction:
    "アカウントの譲渡や売買、評価、代理プレーなど、不正な取引の情報を投稿している",
  advertising_behavior: "宣伝や営業活動を行っている",
  community_original_or_reprint_violation: "コミュニティーの創作や転載に関する規定に違反している",
  other_violations: "その他の規定違反",
  friend_card_request_successfully: "フレンド申請しました。相手が追加するまでお待ちください",
  hi_commander: "こんにちは、指揮官",
  had_add_friend_card: "フレンドカードを追加しました",
  invalid_login_token_tips: "セッションが切れました。もう一度ログインしてください",
  reset_filter: "元に戻す",
  only_see_obtained_nikke: "入手済みのニケだけを表示する",
  voice_mode: "ボイス設定",
  onboarding_mission: "新人歓迎ミッション",
  task_signup_and_signin: "Blabla Linkに登録/ログイン",
  task_bind_lip: "LI Passアカウントを連携",
  task_visite_shiftypad: "SHIFTYPADをチェック&連携",
  task_add_to_desktop: "Blabla Linkをホーム画面に追加",
  task_follow_nikke: "公式アカウントをフォロー",
  daily_mission: "デイリーミッション",
  already_received: "受け取った",
  to_complete: "行く",
  player_set_game_card_to_be_private: "このプレイヤーはゲームカードを非公開に設定にしています",
  equipment_buff_data: "装備ステータス",
  equip_effect_overview: "装備の効果一覧",
  api_code_1200262: "翻訳できませんでした（サーバーエラー、タイムアウトなど）",
  equip_effect_now: "まだ効果を獲得していません",
  api_code_1302116: "フレンド登録が重複しています",
  api_code_101: "サーバーエラー、タイムアウト",
  api_code_102: "サーバーエラー、タイムアウト",
  follow_all: "すべてフォローする",
  all_mission_finished_tips:
    "指揮官、おめでとうございます。すべての新人歓迎ミッションを達成しました",
  item_received_tips: "アイテムを受け取りました。ゲーム内メールボックスを確認してください",
  nikke_power_hint:
    "Web版とゲームクライアント版のプラットフォームの違いにより、ニキの各属性と戦闘力に多少（0～9の範囲）の誤差が生じる可能性があります。現在、誤差解消に取組中ですので、今しばらくお待ちください",
  receive: "受け取る",
  need_bind_lip_first:
    "LI PASSと連携していない場合、クエスト達成によりコインの獲得や報酬の交換ができません。こちらをクリックして連携してください",
  api_code_1300013: "ギフトパックを受け取りました",
  blabla_task_center: "Blablaミッション",
  api_code_1200280: "トピックが存在しません",
  default_error_tips: "ページへのアクセスが集中しています。しばらく経ってからお試しください",
  api_code_1200261: "セッションが切れました。もう一度ログインしてください",
  api_code_212000: "操作が頻繁過ぎます。しばらく経ってからお試しください",
  api_code_212001: "リクエストの頻度が高すぎます。しばらく経ってからお試しください",
  add_to_screen_on_mobile_tips:
    "スマートフォンのブラウザで開き、ブラウザの「共有」をタップした後、「ホーム画面に追加」を選択してください",
  add_to_screen_on_mobile_first:
    "このクエストを達成するには、デスクトップのショートカットからBlabla Linkを一度開く必要があります",
  log_out_switch_region: "ログアウト＆サーバーを切り換える",
  switch_server_to_login: "{0}サーバーを選択する必要がある場合は、{1}してください",
  add_to_screen_finished: "追加しました",
  are_you_sure_to_log_out: "ログアウトしますか？",
  api_code_1100036: "サーバーが違います",
  api_code_1302020:
    "入力エラーが連続したため、現在、CDKコードの交換ができません。72時間経ってから再度お試しください",
  api_code_1302120: "機能をメンテナンス中です。しばらく経ってから再度お試しください",
  view: "見る",
  goto_creatorhub_registre_page_tips: "{0}で登録後、Blabla Linkに戻ってアカウント連携してください",
  goto_creatorhub_forgot_page_tips:
    "{0}でパスワードを再設定後、Blabla Linkに戻ってアカウント連携してください",
  one_click_post: "ワンクリック同期",
  repost: "転送する",
  published_on: "{0}に投稿する",
  auto_sync: "自動で同期",
  my_submissions: "自分の投稿",
  recent_events: "最近のイベント",
  link_creatorhub_tips: "CreatorHubアカウントとBlabla Linkを連携する",
  on_going: "実行中",
  creatorhub_not_bind_list_tips:
    "連携後、CreatorHubの活動をワンクリックでBlabla Linkに同期できます",
  turn_on_auto_sync_title: "自動同期を有効にする",
  turn_on_auto_sync_content:
    "有効にすると、CreatorHubで新規投稿した内容がBlabla Linkに自動で同期されます",
  api_code_1200303: "CreatorHubと連携されていません",
  api_code_1200304: "CreatorHubインターフェースのエラーです",
  api_code_1200305: "CreatorHub同期インターフェースの変更によるエラーです",
  api_code_1200306: "CreatorHubの連携に失敗しました",
  api_code_1200307: "このCreatorHubアカウントはすでに連携されています",
  api_code_1200308: "このCreatorHubアカウントはまだ登録されていません",
  api_code_1200309: "このCreatorHubアカウントは審査中です",
  api_code_1200310: "CreatorHubアカウントのエラーです",
  api_code_1200311: "このプラットフォームはサポートしていません",
  api_code_1200312: "Blabla Linkアカウントはすでに連携されています",
  api_code_1200313: "無効なTokenです",
  api_code_1200314: "このアカウントの認証は却下されました",
  api_code_1200315:
    "アカウントが凍結されているため、内容を同期できません。凍結が解除されたら再度連携してください",
  plat_outpost_desc: "日々のゲームプレイ、コメント、情報共有",
  plat_nikkeart_desc: "二次創作のシェア",
  plat_guides_desc: "NIKKEのゲーム攻略",
  plat_official_desc: "公式ニュースとイベント",
  creatorhub_account_status_0: "アカウントの登録が完了していません",
  creatorhub_account_status_1: "アカウントを審査中です",
  creatorhub_account_status_2: "アカウントは正常です",
  creatorhub_account_status_3: "アカウントの審査が完了していません",
  creatorhub_account_status_4: "アカウント凍結中のため、投稿を同期できませんでした",
  creatorhub_account_status_5: "アカウント凍結中のため、投稿を同期できませんでした",
  creatorhub_account_status_6: "アカウント利用停止中のため、投稿を同期できませんでした",
  creatorhub_account_status_7:
    "このアカウントでは換金のみ利用可能のため、投稿を同期できませんでした",
  api_code_1200316: "続けるにはもう一度ログインして、本人認証を行ってください",
  api_code_1200319: "このユーザーは発言停止されています",
  creatorhub_account_bind_not_verify_tips:
    "アカウントの認証が完了していません。CreatorHubで認証を完了後、Blabla Linkでアカウントを連携してください",
  turn_on: "いますぐオンにする",
  next_time: "あとにする",
  api_code_1200322: "このアカウントは凍結されています",
  top_comment: "コメントをトップに固定",
  bottom_comment: "コメントをボトムに固定",
  cancel_topping: "トップへの固定を解除",
  cancel_bottoming: "ボトムへの固定を解除",
  copy_comment_id: "コメントIDをコピーする",
  comment_id_copied: "コメントIDをコピーしました",
  sure_to_top_comment: "このコメントを人気順のトップに固定しますか？",
  sure_to_bottom_comment: "このコメントを人気順のボトムに固定しますか？",
  sure_to_cancel_toping: "トップへの固定を解除してよろしいですか？",
  sure_to_cancel_bottoming: "ボトムへの固定を解除してよろしいですか？",
  successfully: "成功しました",
  api_code_1200325: "操作に失敗しました。コメントをトップ/ボトムに固定できませんでした",
  api_code_1200326: "操作に失敗しました。権限がありません",
  api_code_1200327: "操作に失敗しました。投稿またはコメントがトップ/ボトムにありません",
  api_code_1200328: "操作に失敗しました。投稿がありません",
  api_code_1200329: "操作に失敗しました。コメントがありません",
  operation_succeeds_and_switch_to_hot_view: "成功しました。人気順に切り替えると表示されます",
  myunion: "私のユニオン",
  square_union_list: "ユニオン一覧",
  share_my_union_and_recruit_members: "私のユニオンをシェアする",
  union_search_placeholder: "ユニオンの名前またはIDをご入力ください",
  success_redeemed_gift_card_tips: "注文詳細ページで注文状況をご確認ください",
  card_no: "ギフトカード番号",
  card_code: "交換コード",
  copied: "コピー完了",
  union_post_title: "ユニオン募集",
  share_to_post: "投稿に追加する",
  certificate_type: "認証タイプ",
  language: "言語",
  entry_level: "加入レベル",
  union_name: "ユニオン名",
  union_activity: "ユニオン活躍度",
  union_rank: "ユニオンランク",
  union_square: "ユニオン広場",
  not_in_union_empty_tips: "まだユニオンに加入していません。下の{0}からユニオンに加入してください",
  union_post_success: "投稿しました",
  share_to_post_detail: "ユニオンカードをシェアして、ユニオンに多くの指揮官を募集しましょう！",
  sku_1: "Amazonギフトカード USD $10（アメリカ）",
  sku_2: "Googleギフトカード USD $10（アメリカ）",
  sku_3: "Amazonギフトカード JPY ¥1,500（日本）",
  sku_4: "Googleギフトカード KRW ₩15,000（韓国）",
  please_select_sku_type: "国・地域を選択してください",
  sku: "国・地域",
  hashtag_manage: "ハッシュタグ管理",
  no_hashtag_for_the_post: "この投稿にはハッシュタグがついていません",
  union_join_type_0: "誰でも加入",
  union_join_type_1: "認証による加入",
  union_join_type_2: "加入不可",
  join_union: "今すぐ加入",
  share_to_square: "ユニオン広場にシェアする",
  save_the_hashtags: "ハッシュタグを保存しますか？",
  confirm_to_edit_hashtag: "保存する",
  union_join_success: "申請しました",
  join_union_detail: "ユニオン{0}に加入しました",
  union_rank_0: "チャレンジャー",
  union_rank_1: "ダイヤ",
  union_rank_2: "プラチナ",
  union_rank_3: "ゴールド",
  union_rank_4: "シルバー",
  union_rank_5: "ブロンズ",
  union_rank_6: "ビギナー",
  had_add_union_card: "ユニオンカードを追加しました",
  area_id_81: "日本",
  area_id_82: "北米",
  area_id_83: "韓国",
  area_id_84: "グローバル",
  area_id_85: "東南アジア",
  area_id_91: "香港/マカオ/台湾",
  join: "加入",
  authoring_statement: "創作に対する声明",
  authoring_statement_category1: "私のオリジナル作品です",
  authoring_statement_category1_1: "出典のURLを挿入する",
  authoring_statement_category1_2: "転載を禁止する",
  authoring_statement_category1_3: "転載を許可する",
  authoring_statement_category2: "本作品はセンシティブな内容を含む可能性があります",
  authoring_statement_category2_1: "ネタバレ",
  authoring_statement_category2_2: "NSFW/流血表現/暴力",
  authoring_statement_category3: "AI生成コンテンツ",
  authoring_statement_tips1: "{0}が含まれる可能性があるため、閲覧にはご注意ください",
  api_code_1303001: "パラメーターエラー",
  api_code_1303002: "システムエラー",
  api_code_1303003: "ユニオンが存在しません",
  api_code_1303004: "サーバー内にあなたのゲームキャラクターが見つかりません",
  api_code_1303005: "ゲームキャラクターが連携されていません",
  api_code_1303006: "ユニオンカードを投稿しました",
  api_code_1303007: "ユニオンカードが投稿されていません",
  api_code_1303008: "連携したゲームキャラクターと一致しません",
  api_code_1303009: "まだユニオンに加入していません",
  api_code_1303010: "まだこのユニオンに加入していません",
  api_code_1303011: "ゲームキャラクターがこのユニオンと異なるサーバーに所属しています",
  api_code_1303012: "この機能はメンテナンス中です。 ",
  api_code_1303013: "指揮官レベルが不足しているため、ユニオンに加入できません",
  api_code_1303014: "ユニオンへの申請が集中しているため、しばらく経ってから再度お試しください",
  api_code_1303015: "この指揮官は利用停止中です",
  api_code_1303016: "ユニオンメンバーが上限に達しました",
  api_code_1303017: "このユニオンには加入申請を送信済みです",
  api_code_1303018: "現在、このユニオンは加入申請を受け付けていません",
  api_code_1303019: "ユニオン{0}に加入申請しました",
  api_code_1303020: "すでに他のユニオンに加入しています",
  api_code_1303021: "ユニオンカードが存在しません",
  api_code_1303022: "現在のユニオンと投稿時のユニオンが異なっています",
  api_code_1303023: "ゲームメンテナンス中です",
  api_code_1303024: "このユニオンは現在のサーバーに存在しません",
  api_code_1100039: "認証コードが承認されませんでした",
  api_code_503001: "コイン不足",
  api_code_503002: "報酬の受け取り上限に達しました",
  api_code_400013: "報酬はすでに配布終了しました",
  api_code_400016: "報酬はすでに配布終了しました",
  api_code_200006: "イベントはまだ開始されていません",
  api_code_200007: "イベントは終了しました",
  gift_card_commodity_name: "【2.5周年】ギフトカード",
  gift_card_commodity_desc:
    "1.交換期間：4月25日～4月30日 毎日11:00更新要求\n2.Amazonギフトカードの種類（選択制）\n・Amazonギフトカード USD $10（アメリカ）\n・Googleギフトカード USD $10（アメリカ）\n・Amazonギフトカード JPY ¥1,500（日本）\n・Googleギフトカード KRW ₩15,000（韓国）\n※交換時にご希望のカード種類を選択してください。\n3.引き換えリンク:・Amazonギフトカード：https://www.amazon.com/gc/redeem  \n・Googleギフトカード：https://play.google.com/redeem  \n4.注意事項\n・Amazonギフトカードは有効期限内にご自身でご使用ください。\n・返品・再販・再配布は禁止されており、保有者はAmazonまたはGoogleの規約を遵守する必要があります。\n・税務・アカウント等に関する問題はすべて保有者の責任となります。\n・運営チームはAmazonギフトカードの引き換え、使用、トラブルに関与いたしません。\n",
  redeem_open_time: "{0}交換開始",
  next_redeem_open_time: "次回交換日：{0}",
  open_for_redemption_soon: "まもなく交換開始します",
  to_apple_official_website_tips: "Appleの公式サイトでギフトカードと交換してください",
  to_google_official_website_tips: "Googleの公式サイトでギフトカードと交換してください",
  to_amazon_official_website_tips: "Amazonの公式サイトでギフトカードと交換してください",
  to_stream_official_website_tips: "Steamの公式サイトでギフトカードと交換してください",
  shiftyspad_user_set_module_private: "ユーザーは{0}を非公開にしています",
  shiftyspad_private_setting_all: "全員に公開",
  shiftyspad_private_setting_friends: "フレンドにのみ公開",
  shiftyspad_private_setting_deny_all: "非公開（自分のみ）",
  shiftyspad_private_setting_allies: "ユニオンメンバーのみに公開",
  already_in_a_union: "ユニオンに加入しています",
  daily_task_refresh_at: "クエストは毎日{0}に更新されます",
  blocking_setting_desc:
    "ブロックすると、このユーザーから投稿した内容が表示されなくなり、やりとりもできなくなります",
  blocking_setting_title: "ブロックしたユーザー",
  blocking_setting: "ブロック設定",
  blocked_at: "{0}にブロックしました",
  unblock: "ブロック解除",
  unblock_this_user: "このユーザーのブロックを解除する",
  block_this_user: "このユーザーをブロックする",
  unblock_this_user_desc: "このユーザーのブロックを解除しますか？",
  block_this_user_desc:
    "ブロックすると、このユーザーが投稿した内容が表示されなくなり、やりとりもできなくなります",
  blocking: "ブロック",
  cannot_block_official: "このユーザーをブロックする（公式アカウントはブロックできません）",
  no_blocked_user: "ブロック中のユーザーはいません",
  allow_friend_request_via_game_card: "プレイカードからのフレンド申請を許可する",
  video_loading_failed: "動画の読み込みに失敗しました",
  video_not_supported_on_this_network: "このネットワークからはYouTube/TikTok動画を再生できません",
  lang_region_en: "英語",
  lang_region_ja: "日本語",
  lang_region_ko: "韓国語",
  lang_region_zh: "中国語（簡体字）",
  "lang_region_zh-TW": "中国語（繁体字）",
  select_language: "言語を選択",
  select_region: "お好みのコンテンツを選択",
  select_region_tips: "以下の言語のコンテンツがおすすめされます",
  region_and_language: "言語とお好みのコンテンツ",
  union: "ユニオン",
  cube_areana: "アリーナ",
  cube_combat: "戦闘",
  api_code_1302122: "このユーザーはフレンド登録を受け付けていません",
  api_code_1302123: "そのユーザーはこのサーバーに接続されていません",
  api_code_1303025: "このユーザーはユニオンの情報を非公開に設定しました",
  api_code_1200354: "相手からブロックされました",
  unlock_condition: "アンロック条件",
  cube: "キューブ",
  item_level: "{0}段階",
  favorite_item: "コレクション",
  api_code_1200256: "ユーザーが存在しません",
  api_code_1200349: "ブロックに失敗しました",
  api_code_1200348: "そのユーザーはすでにブロックしています",
  api_code_1200352: "そのユーザーをブロックしていないため、解除できません",
  api_code_1200353: "ブロック解除失敗",
  please_bind_role_first: "ゲームキャラクターを連携してください",
  api_code_1302124: "相手はフレンドに登録済みです。",
};
